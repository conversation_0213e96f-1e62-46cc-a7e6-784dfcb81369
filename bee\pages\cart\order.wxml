<van-empty wx:if="{{ !paySuccess && !orderInfo }}" description="{{ $t.cart.empty }}" />
<block wx:if="{{ !paySuccess && orderInfo }}">
  <van-cell-group>
    <van-cell title="{{$t.cart.tableNum }}" value="{{ orderInfo.tableCode }}" />
  </van-cell-group>
  <van-cell-group title="{{$t.cart.ordered }}">
    <van-card
      wx:for="{{goodsList}}" wx:key="id"
      price="{{ item.amount }}"
      title="{{ item.goodsName }}"
      desc="{{ item.property }}"
      thumb="{{ item.pic }}"
      num="{{ item.number }}"
      centered
    >
      <view slot="footer">
        <van-tag wx:if="{{ item.cyTableStatus == 0 }}" mark type="warning">"{{$t.cart.Tobeconfirmed }}"</van-tag>
        <van-tag wx:if="{{ item.cyTableStatus == 1 }}" mark type="success">"{{$t.cart.Cooked }}"</van-tag>
        <van-tag wx:if="{{ item.cyTableStatus == 2 }}" mark plain type="success">"{{$t.cart.Served }}"</van-tag>
      </view>
    </van-card>
  </van-cell-group>
  <van-cell-group>
    <van-cell title="{{$t.cart.Numofdishes }}" value="{{ orderInfo.goodsNumber }}" />
    <van-cell title="{{$t.cart.Consumptionamount }}" value="{{ orderInfo.amount }}" />
    <van-cell title="{{$t.cart.Payableamount }}" value="{{ orderInfo.amountReal }}" />
  </van-cell-group>
  <view class="bottom"></view>
  <van-submit-bar label="{{ $t.PickingUp.total }}" price="{{ orderInfo.amountReal*100 }}" button-text="{{ $t.cart.Check }}" bindtap="goPayOrder" />
</block>
<view wx:if="{{ paySuccess }}" class="paySuccess">
  <van-icon name="checked" size="260rpx" color="#07c160" />
  <view class="txt">{{ $t.asset.success }}</view>
</view>

<payment
  money="{{ money }}"
  remark="{{ $t.payment.maidan }}"
  nextAction="{{ nextAction }}"
  show="{{ paymentShow }}"
  bind:cancel="paymentCancel"
  bind:ok="paymentOk"
/>