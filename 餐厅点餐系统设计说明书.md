# 餐厅点餐系统设计说明书

## 1. 项目概述

### 1.1 项目背景
张氏餐饮点餐系统是一套完整的餐厅数字化解决方案，基于RuoYi-Vue框架开发，包含后台管理系统、前端管理界面和微信小程序三个部分，实现餐厅的全流程数字化管理。

### 1.2 项目目标
- 提供完整的餐厅点餐解决方案
- 实现餐品管理、订单管理、支付管理等核心功能
- 支持堂食和外带两种用餐模式
- 提供数据统计和分析功能
- 提升餐厅运营效率和顾客体验

### 1.3 系统特点
- 前后端分离架构
- 微服务模块化设计
- 支持多种支付方式
- 实时订单状态跟踪
- 完善的权限管理体系

## 2. 系统架构设计

### 2.1 总体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   前端管理界面   │    │   后台管理系统   │
│     (bee)       │    │   (ruoyi-ui)    │    │  (ruoyi-admin)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Spring Boot   │
                    │   后端服务层     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库   │
                    │   数据存储层     │
                    └─────────────────┘
```

### 2.2 技术架构

#### 2.2.1 后端技术栈
- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security 5.7.12
- **数据库**: MySQL + MyBatis
- **连接池**: Druid 1.2.23
- **缓存**: Redis
- **文档**: Swagger 3.0.0
- **工具**: Apache POI、FastJSON、JWT

#### 2.2.2 前端技术栈
- **框架**: Vue 2.6.12
- **UI组件**: Element UI 2.15.14
- **状态管理**: Vuex 3.6.0
- **路由**: Vue Router 3.4.9
- **HTTP客户端**: Axios 0.28.1
- **图表**: ECharts 5.4.0

#### 2.2.3 小程序技术栈
- **框架**: 微信小程序原生框架
- **UI组件**: Vant Weapp
- **开发语言**: JavaScript + WXML + WXSS

### 2.3 模块架构

#### 2.3.1 后端模块结构
```
ruoyi-admin/        # 管理后台模块
├── controller/     # 控制器层
├── service/        # 业务逻辑层
├── mapper/         # 数据访问层
└── domain/         # 实体类

ruoyi-common/       # 公共模块
├── annotation/     # 注解定义
├── config/         # 配置类
├── core/           # 核心工具
├── enums/          # 枚举定义
├── exception/      # 异常处理
└── utils/          # 工具类

ruoyi-framework/    # 框架模块
├── config/         # 框架配置
├── datasource/     # 数据源配置
├── interceptor/    # 拦截器
├── security/       # 安全配置
└── web/            # Web配置

ruoyi-system/       # 系统模块
├── domain/         # 系统实体
├── mapper/         # 系统数据访问
└── service/        # 系统服务

ruoyi-generator/    # 代码生成器
ruoyi-quartz/       # 定时任务模块
```

#### 2.3.2 前端模块结构
```
ruoyi-ui/
├── src/
│   ├── api/            # API接口
│   ├── assets/         # 静态资源
│   ├── components/     # 公共组件
│   ├── directive/      # 指令
│   ├── layout/         # 布局组件
│   ├── router/         # 路由配置
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   └── views/          # 页面组件
│       ├── hnmmc/      # 餐厅业务模块
│       ├── system/     # 系统管理模块
│       └── monitor/    # 监控模块
```

#### 2.3.3 小程序模块结构
```
bee/
├── pages/              # 页面
│   ├── home/           # 首页
│   ├── index/          # 点餐页
│   ├── cart/           # 购物车
│   ├── order-details/ # 订单详情
│   ├── pay/            # 支付页面
│   ├── my/             # 个人中心
│   └── ...
├── components/         # 组件
├── utils/              # 工具函数
├── images/             # 图片资源
└── config.js           # 配置文件
```

## 3. 数据库设计

### 3.1 数据库概述
系统采用MySQL关系型数据库，设计了8个核心业务表，支持餐厅点餐的完整业务流程。

### 3.2 核心数据表

#### 3.2.1 餐品分类表 (categories)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| category_id | bigint(20) | 分类ID（主键） |
| parent_id | bigint(20) | 父分类ID |
| category_name | varchar(50) | 分类名称 |
| category_desc | varchar(200) | 分类描述 |
| category_image | varchar(255) | 分类图片URL |
| sort_order | int(4) | 排序序号 |
| level | tinyint(1) | 分类层级（1-3级） |
| status | char(1) | 状态（0禁用 1启用） |

#### 3.2.2 餐品主表 (dishes)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| dish_id | bigint(20) | 餐品ID（主键） |
| category_id | bigint(20) | 分类ID（外键） |
| dish_name | varchar(100) | 餐品名称 |
| dish_desc | text | 餐品描述 |
| dish_image | varchar(255) | 餐品主图URL |
| base_price | decimal(10,2) | 基础价格 |
| current_stock | int(11) | 当前库存 |
| sales_count | int(11) | 销售数量 |
| rating | decimal(3,2) | 评分（1-5分） |
| is_recommended | tinyint(1) | 是否推荐 |
| status | char(1) | 状态（0下架 1上架） |

#### 3.2.3 餐品规格表 (dish_specifications)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| spec_id | bigint(20) | 规格ID（主键） |
| dish_id | bigint(20) | 餐品ID（外键） |
| spec_name | varchar(50) | 规格名称 |
| spec_type | varchar(20) | 规格类型 |
| spec_value | varchar(50) | 规格值 |
| price_adjustment | decimal(10,2) | 价格调整 |
| sort_order | int(4) | 排序序号 |

#### 3.2.4 订单主表 (orders)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| order_id | bigint(20) | 订单ID（主键） |
| order_no | varchar(32) | 订单号 |
| table_no | varchar(20) | 桌号 |
| pickup_no | varchar(20) | 取餐号 |
| customer_name | varchar(50) | 顾客姓名 |
| customer_phone | varchar(20) | 顾客电话 |
| total_amount | decimal(10,2) | 订单总金额 |
| actual_amount | decimal(10,2) | 实际支付金额 |
| order_status | tinyint(2) | 订单状态 |
| order_type | tinyint(1) | 订单类型（1堂食 2外带） |

#### 3.2.5 订单详情表 (order_items)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| item_id | bigint(20) | 订单项ID（主键） |
| order_id | bigint(20) | 订单ID（外键） |
| dish_id | bigint(20) | 餐品ID（外键） |
| dish_name | varchar(100) | 餐品名称 |
| spec_info | text | 规格信息（JSON格式） |
| unit_price | decimal(10,2) | 单价 |
| quantity | int(4) | 数量 |
| subtotal | decimal(10,2) | 小计金额 |

#### 3.2.6 支付记录表 (payments)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| payment_id | bigint(20) | 支付ID（主键） |
| order_id | bigint(20) | 订单ID（外键） |
| payment_no | varchar(64) | 支付流水号 |
| payment_method | tinyint(2) | 支付方式 |
| payment_amount | decimal(10,2) | 支付金额 |
| payment_status | tinyint(2) | 支付状态 |
| payment_time | datetime | 支付时间 |

#### 3.2.7 退款记录表 (refunds)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| refund_id | bigint(20) | 退款ID（主键） |
| order_id | bigint(20) | 订单ID（外键） |
| payment_id | bigint(20) | 支付ID（外键） |
| refund_amount | decimal(10,2) | 退款金额 |
| refund_reason | varchar(200) | 退款原因 |
| refund_status | tinyint(2) | 退款状态 |

#### 3.2.8 广告轮播表 (advertisements)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| ad_id | bigint(20) | 广告ID（主键） |
| ad_title | varchar(100) | 广告标题 |
| ad_image | varchar(255) | 广告图片URL |
| ad_position | tinyint(2) | 广告位置 |
| start_time | datetime | 开始时间 |
| end_time | datetime | 结束时间 |
| status | char(1) | 状态 |

### 3.3 数据字典设计

系统定义了完整的数据字典，包括：

- **订单状态**: 待支付、已支付、制作中、已完成、已取消、已退款
- **订单类型**: 堂食、外带
- **支付方式**: 微信支付、支付宝、现金、银行卡、会员卡
- **支付状态**: 待支付、支付成功、支付失败、已退款
- **退款状态**: 申请中、退款成功、退款失败
- **广告位置**: 首页轮播、分类页横幅、餐品详情页
- **餐品规格类型**: 大小规格、辣度等级、温度选择、甜度选择、加料选择

### 3.4 数据库关系图

```
categories (餐品分类)
    ↓ (1:N)
dishes (餐品) ←─── dish_specifications (餐品规格)
    ↓ (1:N)
order_items (订单详情)
    ↓ (N:1)
orders (订单) ←─── payments (支付记录) ←─── refunds (退款记录)

advertisements (广告轮播) [独立表]
```

## 4. 模块功能设计

### 4.1 后台管理系统功能模块

#### 4.1.1 餐品管理模块 (hnmmc)
- **餐品分类管理** (CategoriesController)
  - 分类的增删改查
  - 多级分类支持
  - 分类排序管理
  - 分类状态控制

- **餐品信息管理** (DishesController)
  - 餐品基本信息维护
  - 餐品图片管理
  - 库存管理
  - 价格管理
  - 上下架控制
  - 推荐设置

- **餐品规格管理** (DishSpecificationsController)
  - 规格类型定义
  - 规格选项配置
  - 价格调整设置
  - 规格排序

#### 4.1.2 订单管理模块
- **订单管理** (OrdersController)
  - 订单列表查询
  - 订单详情查看
  - 订单状态更新
  - 订单统计分析
  - 订单导出功能

- **订单详情管理** (OrderItemsController)
  - 订单项目查看
  - 退款处理
  - 订单修改

#### 4.1.3 支付管理模块
- **支付记录管理** (PaymentsController)
  - 支付流水查询
  - 支付状态跟踪
  - 支付方式统计
  - 异常支付处理

- **退款管理** (RefundsController)
  - 退款申请处理
  - 退款状态跟踪
  - 退款统计分析

#### 4.1.4 营销管理模块
- **广告管理** (AdvertisementsController)
  - 轮播图管理
  - 广告位配置
  - 广告效果统计
  - 定时上下线

#### 4.1.5 系统管理模块
- **用户管理** (SysUserController)
- **角色管理** (SysRoleController)
- **菜单管理** (SysMenuController)
- **部门管理** (SysDeptController)
- **字典管理** (SysDictController)
- **参数配置** (SysConfigController)
- **通知公告** (SysNoticeController)
- **操作日志** (SysOperlogController)
- **登录日志** (SysLogininforController)

### 4.2 前端管理界面功能模块

#### 4.2.1 餐厅业务模块 (/views/hnmmc/)
- **餐品分类管理** (/categories/)
- **餐品管理** (/dishes/)
- **餐品规格管理** (/specifications/)
- **订单管理** (/orders/)
- **订单详情管理** (/items/)
- **支付管理** (/payments/)
- **退款管理** (/refunds/)
- **广告管理** (/advertisements/)

#### 4.2.2 系统管理模块 (/views/system/)
- **用户管理** (/user/)
- **角色管理** (/role/)
- **菜单管理** (/menu/)
- **部门管理** (/dept/)
- **岗位管理** (/post/)
- **字典管理** (/dict/)
- **参数设置** (/config/)
- **通知公告** (/notice/)

#### 4.2.3 系统监控模块 (/views/monitor/)
- **在线用户** (/online/)
- **定时任务** (/job/)
- **数据监控** (/druid/)
- **服务监控** (/server/)
- **缓存监控** (/cache/)
- **操作日志** (/operlog/)
- **登录日志** (/logininfor/)

### 4.3 微信小程序功能模块

#### 4.3.1 核心功能模块
- **首页** (/pages/home/<USER>
  - 轮播图展示
  - 推荐餐品
  - 快捷入口
  - 公告通知

- **点餐模块** (/pages/index/)
  - 餐品分类浏览
  - 餐品列表展示
  - 餐品详情查看
  - 规格选择
  - 购物车管理

- **购物车** (/pages/cart/)
  - 购物车商品管理
  - 数量调整
  - 规格修改
  - 结算功能

- **订单管理** (/pages/order-details/)
  - 订单提交
  - 订单支付
  - 订单状态跟踪
  - 订单历史查看

#### 4.3.2 辅助功能模块
- **支付模块** (/pages/pay/)
  - 多种支付方式
  - 支付状态反馈
  - 支付失败处理

- **取号排队** (/pages/queue/)
  - 排队取号
  - 队列状态查看
  - 叫号提醒

- **个人中心** (/pages/my/)
  - 用户信息管理
  - 订单历史
  - 意见反馈
  - 设置选项

- **会员功能**
  - 会员卡管理 (/pages/card/)
  - 积分管理 (/pages/score/)
  - 资产管理 (/pages/asset/)
  - 优惠券 (/pages/coupons/)

## 5. 接口设计

### 5.1 RESTful API设计规范

系统采用RESTful API设计风格，统一接口规范：

```
GET    /api/{module}/{resource}          # 查询列表
GET    /api/{module}/{resource}/{id}     # 查询详情
POST   /api/{module}/{resource}          # 新增
PUT    /api/{module}/{resource}/{id}     # 修改
DELETE /api/{module}/{resource}/{id}     # 删除
```

### 5.2 核心业务接口

#### 5.2.1 餐品管理接口
```
# 餐品分类
GET    /hnmmc/categories/list           # 分类列表
POST   /hnmmc/categories               # 新增分类
PUT    /hnmmc/categories/{id}          # 修改分类
DELETE /hnmmc/categories/{ids}         # 删除分类

# 餐品管理
GET    /hnmmc/dishes/list              # 餐品列表
GET    /hnmmc/dishes/{id}              # 餐品详情
POST   /hnmmc/dishes                   # 新增餐品
PUT    /hnmmc/dishes/{id}              # 修改餐品
DELETE /hnmmc/dishes/{ids}             # 删除餐品

# 餐品规格
GET    /hnmmc/specifications/list      # 规格列表
POST   /hnmmc/specifications           # 新增规格
PUT    /hnmmc/specifications/{id}      # 修改规格
DELETE /hnmmc/specifications/{ids}     # 删除规格
```

#### 5.2.2 订单管理接口
```
# 订单管理
GET    /hnmmc/orders/list              # 订单列表
GET    /hnmmc/orders/{id}              # 订单详情
POST   /hnmmc/orders                   # 创建订单
PUT    /hnmmc/orders/{id}              # 更新订单
DELETE /hnmmc/orders/{ids}             # 删除订单

# 订单详情
GET    /hnmmc/items/list               # 订单项列表
GET    /hnmmc/items/{id}               # 订单项详情
```

#### 5.2.3 支付管理接口
```
# 支付记录
GET    /hnmmc/payments/list            # 支付列表
GET    /hnmmc/payments/{id}            # 支付详情
POST   /hnmmc/payments                 # 创建支付
PUT    /hnmmc/payments/{id}            # 更新支付状态

# 退款管理
GET    /hnmmc/refunds/list             # 退款列表
POST   /hnmmc/refunds                  # 申请退款
PUT    /hnmmc/refunds/{id}             # 处理退款
```

### 5.3 统一响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 5.4 分页响应格式

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    // 数据列表
  ],
  "total": 100
}
```

## 6. 技术选型说明

### 6.1 后端技术选型

#### 6.1.1 Spring Boot
- **选择理由**: 
  - 快速开发，约定优于配置
  - 丰富的生态系统
  - 内嵌服务器，部署简单
  - 强大的自动配置能力

#### 6.1.2 Spring Security
- **选择理由**:
  - 成熟的安全框架
  - 完善的认证授权机制
  - 支持多种认证方式
  - 与Spring生态无缝集成

#### 6.1.3 MyBatis
- **选择理由**:
  - 灵活的SQL控制
  - 简单易学
  - 性能优秀
  - 支持动态SQL

#### 6.1.4 MySQL
- **选择理由**:
  - 开源免费
  - 性能稳定
  - 社区活跃
  - 支持事务和外键

### 6.2 前端技术选型

#### 6.2.1 Vue.js
- **选择理由**:
  - 渐进式框架，学习成本低
  - 组件化开发
  - 双向数据绑定
  - 丰富的生态系统

#### 6.2.2 Element UI
- **选择理由**:
  - 专为Vue设计的UI组件库
  - 组件丰富，样式统一
  - 文档完善
  - 社区活跃

### 6.3 小程序技术选型

#### 6.3.1 微信小程序原生框架
- **选择理由**:
  - 官方支持，稳定可靠
  - 性能优秀
  - 功能完整
  - 开发工具完善

#### 6.3.2 Vant Weapp
- **选择理由**:
  - 专业的小程序UI组件库
  - 组件丰富，设计精美
  - 性能优化
  - 持续维护

### 6.4 开发工具选型

- **IDE**: IntelliJ IDEA / Visual Studio Code
- **版本控制**: Git
- **构建工具**: Maven (后端) / npm (前端)
- **API文档**: Swagger
- **数据库工具**: Navicat / DataGrip

---

*本文档版本: v1.0*  
*最后更新时间: 2025-07-03*  
*文档维护: 开发团队*
