# 餐厅点餐系统设计说明书

## 1. 项目概述

### 1.1 项目背景
张氏餐饮点餐系统是一套完整的餐厅数字化解决方案，基于RuoYi-Vue框架开发，包含后台管理系统、前端管理界面和微信小程序三个部分，实现餐厅的全流程数字化管理。

### 1.2 项目目标
- 提供完整的餐厅点餐解决方案
- 实现餐品管理、订单管理、支付管理等核心功能
- 支持堂食和外带两种用餐模式
- 提供数据统计和分析功能
- 提升餐厅运营效率和顾客体验

### 1.3 系统特点
- 前后端分离架构
- 微服务模块化设计
- 支持多种支付方式
- 实时订单状态跟踪
- 完善的权限管理体系

## 2. 系统架构设计

### 2.1 总体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   前端管理界面   │    │   后台管理系统   │
│     (bee)       │    │   (ruoyi-ui)    │    │  (ruoyi-admin)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Spring Boot   │
                    │   后端服务层     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库   │
                    │   数据存储层     │
                    └─────────────────┘
```

### 2.2 技术架构

#### 2.2.1 后端技术栈
- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security 5.7.12
- **数据库**: MySQL + MyBatis
- **连接池**: Druid 1.2.23
- **缓存**: Redis
- **文档**: Swagger 3.0.0
- **工具**: Apache POI、FastJSON、JWT

#### 2.2.2 前端技术栈
- **框架**: Vue 2.6.12
- **UI组件**: Element UI 2.15.14
- **状态管理**: Vuex 3.6.0
- **路由**: Vue Router 3.4.9
- **HTTP客户端**: Axios 0.28.1
- **图表**: ECharts 5.4.0

#### 2.2.3 小程序技术栈
- **框架**: 微信小程序原生框架
- **UI组件**: Vant Weapp
- **开发语言**: JavaScript + WXML + WXSS

### 2.3 模块架构

#### 2.3.1 后端模块结构
```
ruoyi-admin/        # 管理后台模块
├── controller/     # 控制器层
├── service/        # 业务逻辑层
├── mapper/         # 数据访问层
└── domain/         # 实体类

ruoyi-common/       # 公共模块
├── annotation/     # 注解定义
├── config/         # 配置类
├── core/           # 核心工具
├── enums/          # 枚举定义
├── exception/      # 异常处理
└── utils/          # 工具类

ruoyi-framework/    # 框架模块
├── config/         # 框架配置
├── datasource/     # 数据源配置
├── interceptor/    # 拦截器
├── security/       # 安全配置
└── web/            # Web配置

ruoyi-system/       # 系统模块
├── domain/         # 系统实体
├── mapper/         # 系统数据访问
└── service/        # 系统服务

ruoyi-generator/    # 代码生成器
ruoyi-quartz/       # 定时任务模块
```

#### 2.3.2 前端模块结构
```
ruoyi-ui/
├── src/
│   ├── api/            # API接口
│   ├── assets/         # 静态资源
│   ├── components/     # 公共组件
│   ├── directive/      # 指令
│   ├── layout/         # 布局组件
│   ├── router/         # 路由配置
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   └── views/          # 页面组件
│       ├── hnmmc/      # 餐厅业务模块
│       ├── system/     # 系统管理模块
│       └── monitor/    # 监控模块
```

#### 2.3.3 小程序模块结构
```
bee/
├── pages/              # 页面
│   ├── home/           # 首页
│   ├── index/          # 点餐页
│   ├── cart/           # 购物车
│   ├── order-details/ # 订单详情
│   ├── pay/            # 支付页面
│   ├── my/             # 个人中心
│   └── ...
├── components/         # 组件
├── utils/              # 工具函数
├── images/             # 图片资源
└── config.js           # 配置文件
```

## 3. 数据库设计

### 3.1 数据库概述
系统采用MySQL关系型数据库，设计了8个核心业务表，支持餐厅点餐的完整业务流程。

### 3.2 核心数据表

#### 3.2.1 餐品分类表 (categories)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| category_id | bigint(20) | 分类ID（主键） |
| parent_id | bigint(20) | 父分类ID |
| category_name | varchar(50) | 分类名称 |
| category_desc | varchar(200) | 分类描述 |
| category_image | varchar(255) | 分类图片URL |
| sort_order | int(4) | 排序序号 |
| level | tinyint(1) | 分类层级（1-3级） |
| status | char(1) | 状态（0禁用 1启用） |

#### 3.2.2 餐品主表 (dishes)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| dish_id | bigint(20) | 餐品ID（主键） |
| category_id | bigint(20) | 分类ID（外键） |
| dish_name | varchar(100) | 餐品名称 |
| dish_desc | text | 餐品描述 |
| dish_image | varchar(255) | 餐品主图URL |
| base_price | decimal(10,2) | 基础价格 |
| current_stock | int(11) | 当前库存 |
| sales_count | int(11) | 销售数量 |
| rating | decimal(3,2) | 评分（1-5分） |
| is_recommended | tinyint(1) | 是否推荐 |
| status | char(1) | 状态（0下架 1上架） |

#### 3.2.3 餐品规格表 (dish_specifications)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| spec_id | bigint(20) | 规格ID（主键） |
| dish_id | bigint(20) | 餐品ID（外键） |
| spec_name | varchar(50) | 规格名称 |
| spec_type | varchar(20) | 规格类型 |
| spec_value | varchar(50) | 规格值 |
| price_adjustment | decimal(10,2) | 价格调整 |
| sort_order | int(4) | 排序序号 |

#### 3.2.4 订单主表 (orders)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| order_id | bigint(20) | 订单ID（主键） |
| order_no | varchar(32) | 订单号 |
| table_no | varchar(20) | 桌号 |
| pickup_no | varchar(20) | 取餐号 |
| customer_name | varchar(50) | 顾客姓名 |
| customer_phone | varchar(20) | 顾客电话 |
| total_amount | decimal(10,2) | 订单总金额 |
| actual_amount | decimal(10,2) | 实际支付金额 |
| order_status | tinyint(2) | 订单状态 |
| order_type | tinyint(1) | 订单类型（1堂食 2外带） |

#### 3.2.5 订单详情表 (order_items)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| item_id | bigint(20) | 订单项ID（主键） |
| order_id | bigint(20) | 订单ID（外键） |
| dish_id | bigint(20) | 餐品ID（外键） |
| dish_name | varchar(100) | 餐品名称 |
| spec_info | text | 规格信息（JSON格式） |
| unit_price | decimal(10,2) | 单价 |
| quantity | int(4) | 数量 |
| subtotal | decimal(10,2) | 小计金额 |

#### 3.2.6 支付记录表 (payments)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| payment_id | bigint(20) | 支付ID（主键） |
| order_id | bigint(20) | 订单ID（外键） |
| payment_no | varchar(64) | 支付流水号 |
| payment_method | tinyint(2) | 支付方式 |
| payment_amount | decimal(10,2) | 支付金额 |
| payment_status | tinyint(2) | 支付状态 |
| payment_time | datetime | 支付时间 |

#### 3.2.7 退款记录表 (refunds)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| refund_id | bigint(20) | 退款ID（主键） |
| order_id | bigint(20) | 订单ID（外键） |
| payment_id | bigint(20) | 支付ID（外键） |
| refund_amount | decimal(10,2) | 退款金额 |
| refund_reason | varchar(200) | 退款原因 |
| refund_status | tinyint(2) | 退款状态 |

#### 3.2.8 广告轮播表 (advertisements)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| ad_id | bigint(20) | 广告ID（主键） |
| ad_title | varchar(100) | 广告标题 |
| ad_image | varchar(255) | 广告图片URL |
| ad_position | tinyint(2) | 广告位置 |
| start_time | datetime | 开始时间 |
| end_time | datetime | 结束时间 |
| status | char(1) | 状态 |

### 3.3 数据字典设计

系统定义了完整的数据字典，包括：

- **订单状态**: 待支付、已支付、制作中、已完成、已取消、已退款
- **订单类型**: 堂食、外带
- **支付方式**: 微信支付、支付宝、现金、银行卡、会员卡
- **支付状态**: 待支付、支付成功、支付失败、已退款
- **退款状态**: 申请中、退款成功、退款失败
- **广告位置**: 首页轮播、分类页横幅、餐品详情页
- **餐品规格类型**: 大小规格、辣度等级、温度选择、甜度选择、加料选择

### 3.4 数据库关系图

```
categories (餐品分类)
    ↓ (1:N)
dishes (餐品) ←─── dish_specifications (餐品规格)
    ↓ (1:N)
order_items (订单详情)
    ↓ (N:1)
orders (订单) ←─── payments (支付记录) ←─── refunds (退款记录)

advertisements (广告轮播) [独立表]
```

### 3.5 系统核心实体类图

```mermaid
classDiagram
    class Categories {
        +Long categoryId
        +Long parentId
        +String categoryName
        +String categoryDesc
        +String categoryImage
        +Integer sortOrder
        +Integer level
        +String status
        +String delFlag
        +Date createTime
        +Date updateTime
        +String createBy
        +String updateBy
        +String remark
        +getSubCategories()
        +isEnabled()
        +isTopLevel()
    }

    class Dishes {
        +Long dishId
        +Long categoryId
        +String dishName
        +String dishDesc
        +String dishImage
        +String dishImages
        +BigDecimal basePrice
        +Integer currentStock
        +Integer warningStock
        +Boolean isUnlimitedStock
        +Integer salesCount
        +BigDecimal rating
        +Boolean isRecommended
        +Boolean isHot
        +String status
        +Date scheduledOnTime
        +Date scheduledOffTime
        +String delFlag
        +Date createTime
        +Date updateTime
        +String createBy
        +String updateBy
        +String remark
        +isAvailable()
        +isInStock()
        +calculateFinalPrice()
        +updateStock()
    }

    class DishSpecifications {
        +Long specId
        +Long dishId
        +String specName
        +String specType
        +String specValue
        +BigDecimal priceAdjustment
        +Integer stockAdjustment
        +Integer sortOrder
        +String status
        +Date createTime
        +Date updateTime
        +String createBy
        +String updateBy
        +isEnabled()
        +getAdjustedPrice()
    }

    class Orders {
        +Long orderId
        +String orderNo
        +String tableNo
        +String pickupNo
        +String customerName
        +String customerPhone
        +BigDecimal totalAmount
        +BigDecimal discountAmount
        +BigDecimal actualAmount
        +Integer orderStatus
        +Integer orderType
        +Integer estimatedTime
        +Integer actualTime
        +Date orderTime
        +Date payTime
        +Date completeTime
        +Date cancelTime
        +String cancelReason
        +Date createTime
        +Date updateTime
        +String createBy
        +String updateBy
        +String remark
        +isPaid()
        +isCompleted()
        +canCancel()
        +canRefund()
        +updateStatus()
        +calculateTotal()
    }

    class OrderItems {
        +Long itemId
        +Long orderId
        +Long dishId
        +String dishName
        +String dishImage
        +String specInfo
        +BigDecimal unitPrice
        +Integer quantity
        +BigDecimal subtotal
        +Integer itemStatus
        +Date createTime
        +Date updateTime
        +calculateSubtotal()
        +isRefundable()
    }

    class Payments {
        +Long paymentId
        +Long orderId
        +String paymentNo
        +String thirdPartyNo
        +Integer paymentMethod
        +BigDecimal paymentAmount
        +Integer paymentStatus
        +Date paymentTime
        +Date callbackTime
        +String callbackData
        +String failureReason
        +Date createTime
        +Date updateTime
        +String createBy
        +String updateBy
        +String remark
        +isSuccessful()
        +isFailed()
        +canRefund()
        +processCallback()
    }

    class Refunds {
        +Long refundId
        +Long orderId
        +Long paymentId
        +String refundNo
        +String thirdPartyRefundNo
        +BigDecimal refundAmount
        +String refundReason
        +Integer refundStatus
        +Date applyTime
        +Date processTime
        +Date callbackTime
        +String callbackData
        +String failureReason
        +String applyBy
        +String processBy
        +Date createTime
        +Date updateTime
        +String remark
        +isProcessing()
        +isSuccessful()
        +isFailed()
        +process()
    }

    class Advertisements {
        +Long adId
        +String adTitle
        +String adDesc
        +String adImage
        +String adLink
        +Integer adPosition
        +Integer sortOrder
        +Date startTime
        +Date endTime
        +Integer clickCount
        +Integer viewCount
        +String status
        +String delFlag
        +Date createTime
        +Date updateTime
        +String createBy
        +String updateBy
        +String remark
        +isActive()
        +isExpired()
        +incrementView()
        +incrementClick()
    }

    %% 关系定义
    Categories ||--o{ Categories : "parent-child"
    Categories ||--o{ Dishes : "contains"
    Dishes ||--o{ DishSpecifications : "has"
    Dishes ||--o{ OrderItems : "ordered as"
    Orders ||--o{ OrderItems : "contains"
    Orders ||--o{ Payments : "paid by"
    Orders ||--o{ Refunds : "refunded by"
    Payments ||--o{ Refunds : "refunded from"

    %% 样式定义
    classDef entityClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef relationClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px

    class Categories,Dishes,DishSpecifications,Orders,OrderItems,Payments,Refunds,Advertisements entityClass
```

该类图展示了系统的核心实体类及其关系：

- **Categories**: 餐品分类实体，支持多级分类结构
- **Dishes**: 餐品主实体，包含价格、库存、销量等信息
- **DishSpecifications**: 餐品规格实体，支持多种规格类型
- **Orders**: 订单主实体，管理订单状态和基本信息
- **OrderItems**: 订单详情实体，记录具体的餐品信息
- **Payments**: 支付记录实体，支持多种支付方式
- **Refunds**: 退款记录实体，处理退款流程
- **Advertisements**: 广告轮播实体，管理营销内容

**主要关系**:
- Categories与Dishes：一对多关系，一个分类包含多个餐品
- Dishes与DishSpecifications：一对多关系，一个餐品有多个规格
- Orders与OrderItems：一对多关系，一个订单包含多个订单项
- Orders与Payments：一对多关系，一个订单可能有多次支付记录
- Payments与Refunds：一对多关系，一次支付可能有多次退款

## 4. 模块功能设计

### 4.1 后台管理系统功能模块

#### 4.1.1 餐品管理模块 (hnmmc)
- **餐品分类管理** (CategoriesController)
  - 分类的增删改查
  - 多级分类支持
  - 分类排序管理
  - 分类状态控制

- **餐品信息管理** (DishesController)
  - 餐品基本信息维护
  - 餐品图片管理
  - 库存管理
  - 价格管理
  - 上下架控制
  - 推荐设置

- **餐品规格管理** (DishSpecificationsController)
  - 规格类型定义
  - 规格选项配置
  - 价格调整设置
  - 规格排序

#### 4.1.2 订单管理模块
- **订单管理** (OrdersController)
  - 订单列表查询
  - 订单详情查看
  - 订单状态更新
  - 订单统计分析
  - 订单导出功能

- **订单详情管理** (OrderItemsController)
  - 订单项目查看
  - 退款处理
  - 订单修改

#### 4.1.3 支付管理模块
- **支付记录管理** (PaymentsController)
  - 支付流水查询
  - 支付状态跟踪
  - 支付方式统计
  - 异常支付处理

- **退款管理** (RefundsController)
  - 退款申请处理
  - 退款状态跟踪
  - 退款统计分析

#### 4.1.4 营销管理模块
- **广告管理** (AdvertisementsController)
  - 轮播图管理
  - 广告位配置
  - 广告效果统计
  - 定时上下线

#### 4.1.5 系统管理模块
- **用户管理** (SysUserController)
- **角色管理** (SysRoleController)
- **菜单管理** (SysMenuController)
- **部门管理** (SysDeptController)
- **字典管理** (SysDictController)
- **参数配置** (SysConfigController)
- **通知公告** (SysNoticeController)
- **操作日志** (SysOperlogController)
- **登录日志** (SysLogininforController)

### 4.2 前端管理界面功能模块

#### 4.2.1 餐厅业务模块 (/views/hnmmc/)
- **餐品分类管理** (/categories/)
- **餐品管理** (/dishes/)
- **餐品规格管理** (/specifications/)
- **订单管理** (/orders/)
- **订单详情管理** (/items/)
- **支付管理** (/payments/)
- **退款管理** (/refunds/)
- **广告管理** (/advertisements/)

#### 4.2.2 系统管理模块 (/views/system/)
- **用户管理** (/user/)
- **角色管理** (/role/)
- **菜单管理** (/menu/)
- **部门管理** (/dept/)
- **岗位管理** (/post/)
- **字典管理** (/dict/)
- **参数设置** (/config/)
- **通知公告** (/notice/)

#### 4.2.3 系统监控模块 (/views/monitor/)
- **在线用户** (/online/)
- **定时任务** (/job/)
- **数据监控** (/druid/)
- **服务监控** (/server/)
- **缓存监控** (/cache/)
- **操作日志** (/operlog/)
- **登录日志** (/logininfor/)

### 4.3 微信小程序功能模块

#### 4.3.1 核心功能模块
- **首页** (/pages/home/<USER>
  - 轮播图展示
  - 推荐餐品
  - 快捷入口
  - 公告通知

- **点餐模块** (/pages/index/)
  - 餐品分类浏览
  - 餐品列表展示
  - 餐品详情查看
  - 规格选择
  - 购物车管理

- **购物车** (/pages/cart/)
  - 购物车商品管理
  - 数量调整
  - 规格修改
  - 结算功能

- **订单管理** (/pages/order-details/)
  - 订单提交
  - 订单支付
  - 订单状态跟踪
  - 订单历史查看

#### 4.3.2 辅助功能模块
- **支付模块** (/pages/pay/)
  - 多种支付方式
  - 支付状态反馈
  - 支付失败处理

- **取号排队** (/pages/queue/)
  - 排队取号
  - 队列状态查看
  - 叫号提醒

- **个人中心** (/pages/my/)
  - 用户信息管理
  - 订单历史
  - 意见反馈
  - 设置选项

- **会员功能**
  - 会员卡管理 (/pages/card/)
  - 积分管理 (/pages/score/)
  - 资产管理 (/pages/asset/)
  - 优惠券 (/pages/coupons/)

## 5. 接口设计

### 5.1 RESTful API设计规范

系统采用RESTful API设计风格，统一接口规范：

```
GET    /api/{module}/{resource}          # 查询列表
GET    /api/{module}/{resource}/{id}     # 查询详情
POST   /api/{module}/{resource}          # 新增
PUT    /api/{module}/{resource}/{id}     # 修改
DELETE /api/{module}/{resource}/{id}     # 删除
```

### 5.2 核心业务接口

#### 5.2.1 餐品管理接口
```
# 餐品分类
GET    /hnmmc/categories/list           # 分类列表
POST   /hnmmc/categories               # 新增分类
PUT    /hnmmc/categories/{id}          # 修改分类
DELETE /hnmmc/categories/{ids}         # 删除分类

# 餐品管理
GET    /hnmmc/dishes/list              # 餐品列表
GET    /hnmmc/dishes/{id}              # 餐品详情
POST   /hnmmc/dishes                   # 新增餐品
PUT    /hnmmc/dishes/{id}              # 修改餐品
DELETE /hnmmc/dishes/{ids}             # 删除餐品

# 餐品规格
GET    /hnmmc/specifications/list      # 规格列表
POST   /hnmmc/specifications           # 新增规格
PUT    /hnmmc/specifications/{id}      # 修改规格
DELETE /hnmmc/specifications/{ids}     # 删除规格
```

#### 5.2.2 订单管理接口
```
# 订单管理
GET    /hnmmc/orders/list              # 订单列表
GET    /hnmmc/orders/{id}              # 订单详情
POST   /hnmmc/orders                   # 创建订单
PUT    /hnmmc/orders/{id}              # 更新订单
DELETE /hnmmc/orders/{ids}             # 删除订单

# 订单详情
GET    /hnmmc/items/list               # 订单项列表
GET    /hnmmc/items/{id}               # 订单项详情
```

#### 5.2.3 支付管理接口
```
# 支付记录
GET    /hnmmc/payments/list            # 支付列表
GET    /hnmmc/payments/{id}            # 支付详情
POST   /hnmmc/payments                 # 创建支付
PUT    /hnmmc/payments/{id}            # 更新支付状态

# 退款管理
GET    /hnmmc/refunds/list             # 退款列表
POST   /hnmmc/refunds                  # 申请退款
PUT    /hnmmc/refunds/{id}             # 处理退款
```

### 5.3 统一响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 5.4 分页响应格式

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    // 数据列表
  ],
  "total": 100
}
```

### 5.5 关键业务流程时序图

#### 5.5.1 顾客点餐流程时序图

```mermaid
sequenceDiagram
    participant C as 顾客(小程序)
    participant UI as 前端界面
    participant API as 后端API
    participant DB as 数据库
    participant Pay as 支付系统

    Note over C,Pay: 顾客点餐完整流程

    %% 1. 浏览餐品
    C->>UI: 打开小程序
    UI->>API: GET /api/categories/list
    API->>DB: 查询餐品分类
    DB-->>API: 返回分类数据
    API-->>UI: 返回分类列表
    UI-->>C: 显示餐品分类

    C->>UI: 选择分类
    UI->>API: GET /api/dishes/list?categoryId=xxx
    API->>DB: 查询分类下餐品
    DB-->>API: 返回餐品数据
    API-->>UI: 返回餐品列表
    UI-->>C: 显示餐品列表

    %% 2. 选择餐品和规格
    C->>UI: 点击餐品详情
    UI->>API: GET /api/dishes/{dishId}
    API->>DB: 查询餐品详情
    API->>DB: 查询餐品规格
    DB-->>API: 返回餐品和规格信息
    API-->>UI: 返回详细信息
    UI-->>C: 显示餐品详情页

    C->>UI: 选择规格和数量
    UI->>UI: 计算价格
    C->>UI: 加入购物车
    UI->>UI: 更新购物车状态

    %% 3. 确认订单
    C->>UI: 点击结算
    UI-->>C: 显示订单确认页
    C->>UI: 填写订单信息(桌号/联系方式)
    C->>UI: 提交订单

    UI->>API: POST /api/orders
    API->>DB: 创建订单记录
    API->>DB: 创建订单详情记录
    API->>DB: 更新餐品库存
    DB-->>API: 返回订单ID
    API-->>UI: 返回订单信息
    UI-->>C: 显示订单详情和支付页面

    %% 4. 支付流程
    C->>UI: 选择支付方式
    C->>UI: 确认支付
    UI->>API: POST /api/payments
    API->>DB: 创建支付记录
    API->>Pay: 调用支付接口
    Pay-->>API: 返回支付结果

    alt 支付成功
        API->>DB: 更新支付状态为成功
        API->>DB: 更新订单状态为已支付
        API-->>UI: 支付成功响应
        UI-->>C: 显示支付成功页面
        UI-->>C: 显示取餐号
    else 支付失败
        API->>DB: 更新支付状态为失败
        API-->>UI: 支付失败响应
        UI-->>C: 显示支付失败页面
        UI-->>C: 提供重新支付选项
    end

    %% 5. 订单状态跟踪
    Note over C,DB: 订单状态实时跟踪
    loop 订单状态查询
        C->>UI: 查看订单状态
        UI->>API: GET /api/orders/{orderId}
        API->>DB: 查询订单状态
        DB-->>API: 返回最新状态
        API-->>UI: 返回订单状态
        UI-->>C: 显示当前状态
    end

    %% 6. 订单完成
    Note over API,DB: 后台订单状态更新
    API->>DB: 更新订单状态为制作中
    API->>DB: 更新订单状态为已完成

    C->>UI: 查看取餐状态
    UI-->>C: 显示可以取餐
    C->>C: 凭取餐号取餐

    Note over C,Pay: 点餐流程结束
```

该时序图展示了顾客从浏览餐品到完成支付的完整流程：

1. **浏览餐品阶段**: 顾客通过小程序浏览餐品分类和具体餐品信息
2. **选择规格阶段**: 顾客选择餐品规格、数量并加入购物车
3. **确认订单阶段**: 顾客填写订单信息并提交订单
4. **支付流程阶段**: 顾客选择支付方式完成支付
5. **状态跟踪阶段**: 顾客实时查看订单制作状态

#### 5.5.2 订单支付流程时序图

```mermaid
sequenceDiagram
    participant C as 顾客
    participant MP as 小程序
    participant API as 后端API
    participant DB as 数据库
    participant WX as 微信支付
    participant ALI as 支付宝

    Note over C,ALI: 订单支付完整流程

    %% 1. 发起支付
    C->>MP: 点击支付按钮
    MP->>API: POST /api/payments/create
    Note right of API: 创建支付记录
    API->>DB: INSERT INTO payments
    API->>DB: 生成支付流水号
    DB-->>API: 返回支付ID
    API-->>MP: 返回支付信息

    %% 2. 选择支付方式
    MP-->>C: 显示支付方式选择
    C->>MP: 选择支付方式

    alt 微信支付
        MP->>API: POST /api/payments/wechat
        API->>WX: 调用微信统一下单API
        WX-->>API: 返回预支付交易会话标识
        API->>DB: 更新支付记录(第三方订单号)
        API-->>MP: 返回支付参数
        MP->>WX: 调起微信支付
        WX-->>MP: 支付结果

        alt 支付成功
            MP->>API: POST /api/payments/callback/wechat
            API->>WX: 验证支付结果
            WX-->>API: 确认支付成功
            API->>DB: 更新支付状态为成功
            API->>DB: 更新订单状态为已支付
            API->>DB: 记录支付时间
            API-->>MP: 支付成功响应
            MP-->>C: 显示支付成功页面
        else 支付失败
            MP->>API: POST /api/payments/fail
            API->>DB: 更新支付状态为失败
            API->>DB: 记录失败原因
            API-->>MP: 支付失败响应
            MP-->>C: 显示支付失败页面
        end

    else 支付宝支付
        MP->>API: POST /api/payments/alipay
        API->>ALI: 调用支付宝下单API
        ALI-->>API: 返回支付宝订单信息
        API->>DB: 更新支付记录
        API-->>MP: 返回支付参数
        MP->>ALI: 调起支付宝支付
        ALI-->>MP: 支付结果

        alt 支付成功
            MP->>API: POST /api/payments/callback/alipay
            API->>ALI: 验证支付结果
            ALI-->>API: 确认支付成功
            API->>DB: 更新支付状态为成功
            API->>DB: 更新订单状态为已支付
            API-->>MP: 支付成功响应
            MP-->>C: 显示支付成功页面
        else 支付失败
            MP->>API: POST /api/payments/fail
            API->>DB: 更新支付状态为失败
            API-->>MP: 支付失败响应
            MP-->>C: 显示支付失败页面
        end

    else 现金支付
        MP->>API: POST /api/payments/cash
        API->>DB: 创建现金支付记录
        API->>DB: 订单状态保持待支付
        API-->>MP: 现金支付创建成功
        MP-->>C: 显示现金支付提示
        Note right of C: 顾客到前台现金支付

        Note over API,DB: 收银员确认现金支付
        API->>DB: 更新支付状态为成功
        API->>DB: 更新订单状态为已支付
        API->>DB: 记录支付时间
    end

    %% 3. 支付后处理
    Note over API,DB: 支付成功后续处理
    API->>DB: 生成取餐号
    API->>DB: 计算预计制作时间
    API->>DB: 更新餐品销量统计

    %% 4. 异步回调处理
    Note over WX,DB: 第三方支付异步回调
    alt 微信支付回调
        WX->>API: POST /api/payments/notify/wechat
        API->>API: 验证回调签名
        API->>DB: 查询支付记录
        API->>DB: 更新支付状态
        API->>DB: 记录回调数据
        API-->>WX: 返回SUCCESS
    else 支付宝回调
        ALI->>API: POST /api/payments/notify/alipay
        API->>API: 验证回调签名
        API->>DB: 查询支付记录
        API->>DB: 更新支付状态
        API->>DB: 记录回调数据
        API-->>ALI: 返回success
    end

    %% 5. 支付状态查询
    loop 支付状态轮询
        MP->>API: GET /api/payments/{paymentId}/status
        API->>DB: 查询支付状态
        DB-->>API: 返回最新状态
        API-->>MP: 返回支付状态
        alt 支付成功
            MP-->>C: 跳转到支付成功页面
        else 支付中
            MP->>MP: 继续轮询
        else 支付失败
            MP-->>C: 显示支付失败，提供重试
        end
    end

    Note over C,ALI: 支付流程结束
```

该时序图详细展示了支付流程的各个环节：

1. **支付发起**: 创建支付记录，生成支付流水号
2. **支付方式选择**: 支持微信支付、支付宝、现金等多种方式
3. **第三方支付**: 调用微信、支付宝等第三方支付接口
4. **支付结果处理**: 处理支付成功/失败的不同情况
5. **异步回调**: 处理第三方支付的异步通知
6. **状态轮询**: 前端轮询支付状态确保及时更新

### 5.6 业务流程活动图

#### 5.6.1 订单处理流程活动图

```mermaid
flowchart TD
    A[顾客下单] --> B{检查餐品库存}
    B -->|库存充足| C[创建订单]
    B -->|库存不足| D[提示库存不足]
    D --> E[顾客重新选择]
    E --> B

    C --> F[生成订单号]
    F --> G[计算订单金额]
    G --> H[创建订单详情]
    H --> I[更新餐品库存]
    I --> J[订单状态: 待支付]

    J --> K{顾客是否支付}
    K -->|超时未支付| L[自动取消订单]
    L --> M[恢复餐品库存]
    M --> N[订单状态: 已取消]

    K -->|顾客支付| O[验证支付结果]
    O --> P{支付是否成功}
    P -->|支付失败| Q[订单状态: 待支付]
    Q --> K

    P -->|支付成功| R[订单状态: 已支付]
    R --> S[生成取餐号]
    S --> T[计算预计制作时间]
    T --> U[通知厨房制作]

    U --> V[订单状态: 制作中]
    V --> W{制作是否完成}
    W -->|制作中| X[更新制作进度]
    X --> W

    W -->|制作完成| Y[订单状态: 已完成]
    Y --> Z[语音叫号]
    Z --> AA[顾客取餐]
    AA --> BB[订单流程结束]

    %% 异常处理流程
    V --> CC{顾客申请退款}
    CC -->|申请退款| DD[检查退款条件]
    DD --> EE{是否可退款}
    EE -->|可以退款| FF[处理退款]
    EE -->|不可退款| GG[拒绝退款申请]

    FF --> HH[退还支付金额]
    HH --> II[恢复餐品库存]
    II --> JJ[订单状态: 已退款]
    JJ --> BB

    GG --> V

    %% 管理员操作流程
    R --> KK{管理员操作}
    KK -->|取消订单| LL[管理员取消]
    LL --> MM[处理退款流程]
    MM --> FF

    KK -->|修改订单| NN[修改订单信息]
    NN --> OO[重新计算金额]
    OO --> PP{金额是否变化}
    PP -->|需要补款| QQ[生成补款订单]
    PP -->|需要退款| RR[处理差额退款]
    PP -->|金额不变| V
    QQ --> K
    RR --> FF

    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px

    class A,BB startEnd
    class C,F,G,H,I,O,R,S,T,U,V,Y,Z,AA,FF,HH,II,LL,MM,NN,OO process
    class B,K,P,W,CC,EE,KK,PP decision
    class D,L,M,N,Q,GG error
```

该活动图展示了从顾客下单到订单完成的完整业务流程：

**主要流程节点**:
1. **订单创建**: 检查库存、生成订单号、计算金额
2. **支付处理**: 支付验证、状态更新、取餐号生成
3. **制作流程**: 通知厨房、状态跟踪、完成通知
4. **异常处理**: 超时取消、退款申请、管理员操作

**关键决策点**:
- 库存检查：确保餐品有足够库存
- 支付验证：确认支付结果的真实性
- 制作状态：跟踪订单制作进度
- 退款条件：判断是否满足退款要求

#### 5.6.2 退款处理流程活动图

```mermaid
flowchart TD
    A[退款申请] --> B{申请来源}
    B -->|顾客申请| C[顾客填写退款原因]
    B -->|管理员发起| D[管理员选择退款原因]

    C --> E[提交退款申请]
    D --> E
    E --> F[系统生成退款单号]
    F --> G[退款状态: 申请中]

    G --> H{检查退款条件}
    H -->|订单未支付| I[拒绝退款申请]
    H -->|订单已完成| J{是否超过退款时限}
    H -->|订单制作中| K{是否已开始制作}
    H -->|订单已支付| L[允许退款]

    J -->|超过时限| I
    J -->|未超过时限| L
    K -->|已开始制作| M{是否允许强制退款}
    K -->|未开始制作| L

    M -->|不允许| I
    M -->|允许| N[扣除制作成本]
    N --> O[计算实际退款金额]
    O --> L

    I --> P[退款状态: 退款失败]
    P --> Q[通知申请人]
    Q --> R[退款流程结束]

    L --> S{确定退款金额}
    S --> T[创建退款记录]
    T --> U{支付方式}

    U -->|微信支付| V[调用微信退款API]
    U -->|支付宝| W[调用支付宝退款API]
    U -->|现金支付| X[标记现金退款]
    U -->|银行卡| Y[人工处理银行卡退款]

    V --> Z{微信退款结果}
    W --> AA{支付宝退款结果}
    X --> BB[退款状态: 退款成功]
    Y --> CC[等待人工确认]

    Z -->|成功| BB
    Z -->|失败| DD[退款状态: 退款失败]
    AA -->|成功| BB
    AA -->|失败| DD

    CC --> EE{人工确认结果}
    EE -->|确认成功| BB
    EE -->|确认失败| DD

    BB --> FF[更新订单状态为已退款]
    FF --> GG[恢复餐品库存]
    GG --> HH[记录退款完成时间]
    HH --> II[发送退款成功通知]
    II --> JJ[更新财务记录]
    JJ --> R

    DD --> KK[记录失败原因]
    KK --> LL[发送退款失败通知]
    LL --> MM{是否重试}
    MM -->|重试| S
    MM -->|不重试| R

    %% 异步回调处理
    NN[第三方支付退款回调] --> OO{验证回调数据}
    OO -->|验证成功| PP[更新退款状态]
    OO -->|验证失败| QQ[记录异常日志]
    PP --> RR{回调状态}
    RR -->|退款成功| BB
    RR -->|退款失败| DD
    QQ --> SS[人工处理异常]
    SS --> PP

    %% 管理员审核流程
    G --> TT{是否需要审核}
    TT -->|需要审核| UU[等待管理员审核]
    TT -->|无需审核| H
    UU --> VV{审核结果}
    VV -->|审核通过| H
    VV -->|审核拒绝| I

    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef waiting fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px

    class A,R startEnd
    class C,D,E,F,T,V,W,X,Y,FF,GG,HH,II,JJ,KK,LL,PP,SS process
    class B,H,J,K,M,S,U,Z,AA,EE,MM,OO,RR,TT,VV decision
    class I,P,DD,QQ error
    class BB,L success
    class G,CC,UU waiting
```

该活动图详细展示了退款处理的完整流程：

**退款触发条件**:
- 顾客主动申请退款
- 管理员发起退款
- 系统自动退款（如超时等）

**退款处理步骤**:
1. **条件检查**: 验证订单状态和退款时限
2. **金额计算**: 根据制作进度计算实际退款金额
3. **退款执行**: 调用第三方支付退款接口
4. **状态更新**: 更新订单状态和库存信息
5. **通知发送**: 向相关方发送退款结果通知

**异常处理**:
- 退款失败重试机制
- 人工审核流程
- 异步回调处理

## 6. 技术选型说明

### 6.1 后端技术选型

#### 6.1.1 Spring Boot
- **选择理由**: 
  - 快速开发，约定优于配置
  - 丰富的生态系统
  - 内嵌服务器，部署简单
  - 强大的自动配置能力

#### 6.1.2 Spring Security
- **选择理由**:
  - 成熟的安全框架
  - 完善的认证授权机制
  - 支持多种认证方式
  - 与Spring生态无缝集成

#### 6.1.3 MyBatis
- **选择理由**:
  - 灵活的SQL控制
  - 简单易学
  - 性能优秀
  - 支持动态SQL

#### 6.1.4 MySQL
- **选择理由**:
  - 开源免费
  - 性能稳定
  - 社区活跃
  - 支持事务和外键

### 6.2 前端技术选型

#### 6.2.1 Vue.js
- **选择理由**:
  - 渐进式框架，学习成本低
  - 组件化开发
  - 双向数据绑定
  - 丰富的生态系统

#### 6.2.2 Element UI
- **选择理由**:
  - 专为Vue设计的UI组件库
  - 组件丰富，样式统一
  - 文档完善
  - 社区活跃

### 6.3 小程序技术选型

#### 6.3.1 微信小程序原生框架
- **选择理由**:
  - 官方支持，稳定可靠
  - 性能优秀
  - 功能完整
  - 开发工具完善

#### 6.3.2 Vant Weapp
- **选择理由**:
  - 专业的小程序UI组件库
  - 组件丰富，设计精美
  - 性能优化
  - 持续维护

### 6.4 开发工具选型

- **IDE**: IntelliJ IDEA / Visual Studio Code
- **版本控制**: Git
- **构建工具**: Maven (后端) / npm (前端)
- **API文档**: Swagger
- **数据库工具**: Navicat / DataGrip

---

*本文档版本: v1.0*  
*最后更新时间: 2025-07-03*  
*文档维护: 开发团队*
