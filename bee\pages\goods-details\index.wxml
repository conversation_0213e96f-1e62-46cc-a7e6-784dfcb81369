<view class="container">
  <view class="swiper-container" id="swiper-container">
    <swiper class="swiper_box" indicator-dots="true" indicator-active-color="#fff"
      autoplay="{{!goodsDetail.basicInfo.videoId}}" circular>
      <swiper-item wx:if="{{goodsDetail.basicInfo.videoId}}">
        <video src="{{videoMp4Src}}" autoplay="true" loop="true" style='width:100%;height:100%;'></video>
      </swiper-item>
      <swiper-item wx:for="{{goodsDetail.pics}}" wx:key="id">
        <image src="{{item.pic}}" class="slide-image" mode="aspectFill" lazy-load="true" />
      </swiper-item>
    </swiper>
  </view>
  <view class="goods-info">
    <view class="goods-info-top-container">
      <view class="goods-profile">
        <view class="p"><text>¥</text> {{selectSizePrice}}</view>
        <view wx:if="{{goodsDetail.basicInfo.originalPrice && goodsDetail.basicInfo.originalPrice > 0}}"
          class="goods-price" style='color:#aaa;text-decoration:line-through;padding: 15rpx 0rpx 0rpx 15rpx;'>
          <text>¥</text> {{selectSizeOPrice}}
        </view>
      </view>
    </view>
    <view class="goods-title">{{goodsDetail.basicInfo.name}}</view>
    <view class="characteristic">{{goodsDetail.basicInfo.characteristic}}</view>
    <view class="goods-share" wx:if="{{goodsDetail.basicInfo.commissionType == 1}}">{{ $t.goodsDetail.commission1 }}
      {{goodsDetail.basicInfo.commission}} {{ $t.goodsDetail.commission2 }}</view>
    <view class="goods-share" wx:if="{{goodsDetail.basicInfo.commissionType == 2}}">{{ $t.goodsDetail.commission1 }}
      {{goodsDetail.basicInfo.commission}} {{ $t.goodsDetail.commission3 }}</view>
  </view>
  <van-cell-group wx:if="{{curGoodsKanjia}}" title="{{ $t.goodsDetail.kanJiaSetting }}">
    <van-cell title="{{ $t.goodsDetail.limit }}" value="{{curGoodsKanjia.number}}" />
    <van-cell title="{{ $t.goodsDetail.saledNum }}" value="{{curGoodsKanjia.numberBuy}}" />
    <van-cell title="{{ $t.goodsDetail.originalPrice }}" value="￥{{curGoodsKanjia.originalPrice}}" />
    <van-cell title="{{ $t.goodsDetail.minPrice }}" value="￥{{curGoodsKanjia.minPrice}}" />
    <van-cell title="{{ $t.goodsDetail.end }}" value="{{curGoodsKanjia.dateEnd}}" />
  </van-cell-group>
  <view class="curKanjiaprogress" wx:if="{{curKanjiaprogress}}">
    <view class="name">{{ $t.goodsDetail.help }}
      <text style='color:red;font-weight:bold;'>{{curKanjiaprogress.joiner.nick}}</text> {{ $t.goodsDetail.kanjia }}！
    </view>
    <van-progress
      percentage="{{100 * (curGoodsKanjia.originalPrice - curKanjiaprogress.kanjiaInfo.curPrice) / (curGoodsKanjia.originalPrice - curGoodsKanjia.minPrice)}}"
      pivot-text="￥{{curKanjiaprogress.kanjiaInfo.curPrice}}"
      pivot-color="#1aad19"
      color="#1aad19"
      custom-class="van-progress"
    />
    <view class="flex">
      <view class="kjbutton">
        <button type="primary" open-type="share">{{ $t.goodsDetail.inviteKanJiaFriend }}</button>
      </view>
    </view>
    <van-cell title="{{ $t.goodsDetail.helpPerson }}" value="{{curKanjiaprogress.kanjiaInfo .helpNumber}}" />
    <van-cell title="{{ $t.goodsDetail.status }}" value="{{curKanjiaprogress.kanjiaInfo .statusStr}}" />
    <van-cell title="{{ $t.goodsDetail.joinTime }}" value="{{curKanjiaprogress.kanjiaInfo .dateAdd}}" />
  </view>
  <van-cell wx:if="{{hasMoreSelect && goodsDetailSkuShowType==0}}" custom-class="vw100 goods-property-container"
    border="{{false}}" is-link bind:click="bindGuiGeTap">
    <view slot="title">
      {{ $t.common.select }}:
      <block wx:for="{{goodsDetail.properties}}" wx:key="id"> {{item.name}}</block>
      <block wx:for="{{goodsAddition}}" wx:key="id"> {{item.name}}</block>
    </view>
  </van-cell>
  <view class="size-label-box2" wx:if="{{goodsDetailSkuShowType==1}}">
    <view class="label-title">{{ $t.goodsDetail.noSelectSku }}</view>
    <view class="size-label-box">
      <block wx:for="{{goodsDetail.properties}}" wx:for-item="property" wx:for-index="idx" wx:key="id">
        <view class="label">{{property.name}}</view>
        <view class="label-item-box">
          <view class="label-item {{item.active ? 'active' : '' }}" wx:for="{{property.childsCurGoods}}" wx:key="id"
            bindtap="labelItemTap" data-propertyindex="{{idx}}" data-propertychildindex="{{index}}">
            {{item.name}}
          </view>
        </view>
      </block>
    </view>
    <van-cell title="{{ $t.goodsDetail.buyNumber }}">
      <view>
        <van-stepper value="{{ buyNumber }}" min="{{ buyNumMin }}" max="{{ buyNumMax }}" bind:change="stepChange" />
      </view>
    </van-cell>
  </view>
  <view wx:if="{{shopSubdetail}}" class="shop-container">
    <image mode="aspectFill" src="{{shopSubdetail.info.pic}}"></image>
    <view class="info">
      <view class="title">{{shopSubdetail.info.name}}</view>
      <view class="address">{{shopSubdetail.info.address}}</view>
    </view>
  </view>
  <view class="goods-des-info" id="goods-des-info">
    <view class="label-title">
      <view class="left">{{ $t.goodsDetail.title }}</view>
    </view>
    <view class="goods-text">
      <parser html="{{goodsDetail.content}}" />
    </view>
  </view>
  <van-cell-group wx:if="{{curKanjiaprogress && curKanjiaprogress.helps && curKanjiaprogress.helps.length>0}}" title="{{ $t.goodsDetail.kanjiaLogs }}">
    <view class="kjlj"  wx:for="{{curKanjiaprogress.helps}}" wx:key="*this">
      <image class="kjlj-l" src="{{item.avatarUrl}}" mode="aspectFill" />
      <van-cell custom-class="kjlj-r" title="￥ {{item.cutPrice}}" label="{{item.nick}} {{item.dateAdd}} {{ $t.goodsDetail.help2 }}" size="large" />
    </view>
    <view style="height: 64rpx;"></view>
  </van-cell-group>
  <view class='kjBuyButton' wx:if="{{curGoodsKanjia && curKanjiaprogress}}">
    <view class="item" wx:if="{{curKanjiaprogress.kanjiaInfo.uid != curuid}}">
      <van-button type="primary" block bind:click="helpKanjia" disabled="{{myHelpDetail}}">
        {{myHelpDetail ? $t.goodsDetail.helped : $t.goodsDetail.helphe}}</van-button>
    </view>
    <view class="item" wx:else>
      <van-button type="danger" block bind:click="tobuy">{{ $t.goodsDetail.buyUseCurPrice }}</van-button>
    </view>
  </view>
  <van-goods-action wx:if="{{ goodsDetail.basicInfo.stores < goodsDetail.basicInfo.minBuyNumber }}">
    <van-goods-action-button text='{{ $t.goodsDetail.storeing }}' type="warning" disabled />
  </van-goods-action>
  <van-goods-action wx:else>
    <van-goods-action-button wx:if="{{ curKanjiaprogress }}" text='{{myHelpDetail ? $t.goodsDetail.helped : $t.goodsDetail.helphe}}' type="danger" bind:click="helpKanjia" disabled="{{ myHelpDetail }}" />
    <van-goods-action-button wx:if="{{ goodsDetail.basicInfo.kanjia && !curGoodsKanjia }}" text="{{ $t.goodsDetail.ended }}" type="warning" disabled />
    <van-goods-action-button wx:elif="{{curGoodsKanjia && (!curKanjiaprogress || curKanjiaprogress.kanjiaInfo.uid != curuid)}}" text="{{ $t.goodsDetail.kanjiaBtn }}" type="warning" bind:click="joinKanjia" />
    <van-goods-action-button wx:elif="{{ curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid == curuid }}" text="￥{{curKanjiaprogress.kanjiaInfo.curPrice}} {{ $t.goodsDetail.buy }}" type="warning" bind:click="{{goodsDetailSkuShowType==0?'toAddShopCar':'addShopCar'}}" />
    <van-goods-action-button wx:else text="{{ $t.goodsDetail.addCartBtn }}" type="warning" bind:click="{{goodsDetailSkuShowType==0?'toAddShopCar':'addShopCar'}}" />
  </van-goods-action>
</view>

<block wx:if="{{posterShow}}">
  <view class="poster-mask"></view>
  <view class="poster">
    <canvas class="canvas" style="{{canvasstyle}}" canvas-id="firstCanvas"></canvas>
  </view>
  <view class="poster-btn">
    <button type="primary" size="mini" bindtap='_saveToMobile'> {{ $t.goodsDetail.saveImage }} </button>
    <button type="warn" size="mini" bindtap='closePop'> {{ $t.common.cancel }} </button>
  </view>
</block>

<poster id="poster" config="{{posterConfig}}" bind:success="onPosterSuccess" bind:fail="onPosterFail"></poster>
<view wx:if="{{showposterImg}}" class="popup-mask"></view>
<view wx:if="{{showposterImg}}" class="posterImg-box">
  <image mode="widthFix" class="posterImg" src="{{posterImg}}"></image>
  <view class="btn-create" bindtap="savePosterPic">{{ $t.goodsDetail.saveImage }}</view>
</view>

<van-popup show="{{ !hideShopPopup }}" round closeable position="bottom"
  custom-style="padding-top:48rpx;max-height: 80%;" bind:close="closePopupTap">
  <van-card centered price="{{ selectSizePrice }}"
    origin-price="{{ selectSizePrice != selectSizePrice ? selectSizeOPrice : '' }}"
    title="{{ goodsDetail.basicInfo.name }}" thumb="{{ skuGoodsPic }}" />
  <view class="size-label-box">
    <block wx:for="{{goodsDetail.properties}}" wx:for-item="property" wx:for-index="idx" wx:key="id">
      <view class="label">{{property.name}}</view>
      <view class="label-item-box">
        <view class="label-item {{item.active ? 'active' : '' }}" wx:for="{{property.childsCurGoods}}" wx:key="id"
          bindtap="labelItemTap" data-propertyindex="{{idx}}" data-propertychildindex="{{index}}">
          {{item.name}}
        </view>
      </view>
    </block>
    <block wx:for="{{goodsAddition}}" wx:for-item="property" wx:for-index="idx" wx:key="id">
      <view class="label">{{property.name}}</view>
      <view class="label-item-box">
        <view class="label-item {{item.active ? 'active' : '' }}" wx:for="{{property.items}}" wx:key="id"
          bindtap="labelItemTap2" data-propertyindex="{{idx}}" data-propertychildindex="{{index}}">
          {{item.name}}
        </view>
      </view>
    </block>
    <block wx:for="{{goodsTimesSchedule}}" wx:for-item="property" wx:key="id">
      <view class="label">{{property.day}}</view>
      <view class="label-item-box">
        <view class="label-item {{item.active ? 'active' : '' }}" wx:for-index="index2" wx:for="{{property.items}}" wx:key="id"
          bindtap="skuClick3" data-idx1="{{index}}" data-idx2="{{index2}}">
          {{item.name}}
        </view>
      </view>
    </block>
  </view>
  <van-cell wx:if="{{ !(curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid == curuid) }}" title="{{ $t.goodsDetail.buyNumber }}">
    <view>
      <van-stepper value="{{ buyNumber }}" min="{{ buyNumMin }}" max="{{ buyNumMax }}" bind:change="stepChange" />
    </view>
  </van-cell>
  <van-button wx:if="{{ curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid == curuid }}" bindtap="addShopCar" type="danger" block>￥{{curKanjiaprogress.kanjiaInfo.curPrice}} {{ $t.goodsDetail.buy }}</van-button>
  <van-button wx:else bindtap="addShopCar" type="danger" block>{{ $t.goodsDetail.addCartBtn }}</van-button>
</van-popup>