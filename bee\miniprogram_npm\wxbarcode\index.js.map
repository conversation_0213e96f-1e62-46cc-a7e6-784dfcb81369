{"version": 3, "sources": ["index.js", "demo/utils/index.js", "demo/utils/barcode.js", "demo/utils/qrcode.js"], "names": [], "mappings": ";;;;;;;AAAA;;;ACAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = require('./demo/utils')", "var barcode = require('./barcode');\r\nvar qrcode = require('./qrcode');\r\n\r\nfunction convert_length(length) {\r\n\treturn Math.round(wx.getSystemInfoSync().windowWidth * length / 750);\r\n}\r\n\r\nfunction barc(id, code, width, height) {\r\n\tbarcode.code128(wx.createCanvasContext(id), code, convert_length(width), convert_length(height))\r\n}\r\n\r\nfunction qrc(id, code, width, height) {\r\n\tqrcode.api.draw(code, {\r\n\t\tctx: wx.createCanvasContext(id),\r\n\t\twidth: convert_length(width),\r\n\t\theight: convert_length(height)\r\n\t})\r\n}\r\n\r\nmodule.exports = {\r\n\tbarcode: barc,\r\n\tqrcode: qrc\r\n}", "var CHAR_TILDE = 126;\nvar CODE_FNC1 = 102;\n\nvar SET_STARTA = 103;\nvar SET_STARTB = 104;\nvar SET_STARTC = 105;\nvar SET_SHIFT = 98;\nvar SET_CODEA = 101;\nvar SET_CODEB = 100;\nvar SET_STOP = 106;\n\n\nvar REPLACE_CODES = {\n    CHAR_TILDE: CODE_FNC1 //~ corresponds to FNC1 in GS1-128 standard\n}\n\nvar CODESET = {\n    ANY: 1,\n    AB: 2,\n    A: 3,\n    B: 4,\n    C: 5\n};\n\nfunction getBytes(str) {\n    var bytes = [];\n    for (var i = 0; i < str.length; i++) {\n        bytes.push(str.charCodeAt(i));\n    }\n    return bytes;\n}\n\nexports.code128 = function (ctx, text, width, height) {\n\n    width = parseInt(width);\n\n    height = parseInt(height);\n\n    var codes = stringToCode128(text);\n\n    var g = new Graphics(ctx, width, height);\n\n    var barWeight = g.area.width / ((codes.length - 3) * 11 + 35);\n\n    var x = g.area.left;\n    var y = g.area.top;\n    for (var i = 0; i < codes.length; i++) {\n        var c = codes[i];\n        //two bars at a time: 1 black and 1 white\n        for (var bar = 0; bar < 8; bar += 2) {\n            var barW = PATTERNS[c][bar] * barWeight;\n            // var barH = height - y - this.border;\n            var barH = height - y;\n            var spcW = PATTERNS[c][bar + 1] * barWeight;\n\n            //no need to draw if 0 width\n            if (barW > 0) {\n                g.fillFgRect(x, y, barW, barH);\n            }\n\n            x += barW + spcW;\n        }\n    }\n\n    ctx.draw();\n}\n\n\nfunction stringToCode128(text) {\n\n    var barc = {\n        currcs: CODESET.C\n    };\n\n    var bytes = getBytes(text);\n    //decide starting codeset\n    var index = bytes[0] == CHAR_TILDE ? 1 : 0;\n\n    var csa1 = bytes.length > 0 ? codeSetAllowedFor(bytes[index++]) : CODESET.AB;\n    var csa2 = bytes.length > 0 ? codeSetAllowedFor(bytes[index++]) : CODESET.AB;\n    barc.currcs = getBestStartSet(csa1, csa2);\n    barc.currcs = perhapsCodeC(bytes, barc.currcs);\n\n    //if no codeset changes this will end up with bytes.length+3\n    //start, checksum and stop\n    var codes = new Array();\n\n    switch (barc.currcs) {\n        case CODESET.A:\n            codes.push(SET_STARTA);\n            break;\n        case CODESET.B:\n            codes.push(SET_STARTB);\n            break;\n        default:\n            codes.push(SET_STARTC);\n            break;\n    }\n\n\n    for (var i = 0; i < bytes.length; i++) {\n        var b1 = bytes[i]; //get the first of a pair\n        //should we translate/replace\n        if (b1 in REPLACE_CODES) {\n            codes.push(REPLACE_CODES[b1]);\n            i++ //jump to next\n            b1 = bytes[i];\n        }\n\n        //get the next in the pair if possible\n        var b2 = bytes.length > (i + 1) ? bytes[i + 1] : -1;\n\n        codes = codes.concat(codesForChar(b1, b2, barc.currcs));\n        //code C takes 2 chars each time\n        if (barc.currcs == CODESET.C) i++;\n    }\n\n    //calculate checksum according to Code 128 standards\n    var checksum = codes[0];\n    for (var weight = 1; weight < codes.length; weight++) {\n        checksum += (weight * codes[weight]);\n    }\n    codes.push(checksum % 103);\n\n    codes.push(SET_STOP);\n\n    //encoding should now be complete\n    return codes;\n\n    function getBestStartSet(csa1, csa2) {\n        //tries to figure out the best codeset\n        //to start with to get the most compact code\n        var vote = 0;\n        vote += csa1 == CODESET.A ? 1 : 0;\n        vote += csa1 == CODESET.B ? -1 : 0;\n        vote += csa2 == CODESET.A ? 1 : 0;\n        vote += csa2 == CODESET.B ? -1 : 0;\n        //tie goes to B due to my own predudices\n        return vote > 0 ? CODESET.A : CODESET.B;\n    }\n\n    function perhapsCodeC(bytes, codeset) {\n        for (var i = 0; i < bytes.length; i++) {\n            var b = bytes[i]\n            if ((b < 48 || b > 57) && b != CHAR_TILDE)\n                return codeset;\n        }\n        return CODESET.C;\n    }\n\n    //chr1 is current byte\n    //chr2 is the next byte to process. looks ahead.\n    function codesForChar(chr1, chr2, currcs) {\n        var result = [];\n        var shifter = -1;\n\n        if (charCompatible(chr1, currcs)) {\n            if (currcs == CODESET.C) {\n                if (chr2 == -1) {\n                    shifter = SET_CODEB;\n                    currcs = CODESET.B;\n                }\n                else if ((chr2 != -1) && !charCompatible(chr2, currcs)) {\n                    //need to check ahead as well\n                    if (charCompatible(chr2, CODESET.A)) {\n                        shifter = SET_CODEA;\n                        currcs = CODESET.A;\n                    }\n                    else {\n                        shifter = SET_CODEB;\n                        currcs = CODESET.B;\n                    }\n                }\n            }\n        }\n        else {\n            //if there is a next char AND that next char is also not compatible\n            if ((chr2 != -1) && !charCompatible(chr2, currcs)) {\n                //need to switch code sets\n                switch (currcs) {\n                    case CODESET.A:\n                        shifter = SET_CODEB;\n                        currcs = CODESET.B;\n                        break;\n                    case CODESET.B:\n                        shifter = SET_CODEA;\n                        currcs = CODESET.A;\n                        break;\n                }\n            }\n            else {\n                //no need to shift code sets, a temporary SHIFT will suffice\n                shifter = SET_SHIFT;\n            }\n        }\n\n        //ok some type of shift is nessecary\n        if (shifter != -1) {\n            result.push(shifter);\n            result.push(codeValue(chr2));\n        }\n        else {\n            if (currcs == CODESET.C) {\n                //include next as well\n                result.push(codeValue(chr1, chr2));\n            }\n            else {\n                result.push(codeValue(chr1));\n            }\n        }\n        barc.currcs = currcs;\n\n        return result;\n    }\n}\n\n//reduce the ascii code to fit into the Code128 char table\nfunction codeValue(chr1, chr2) {\n    if (typeof chr2 == \"undefined\") {\n        return chr1 >= 32 ? chr1 - 32 : chr1 + 64;\n    }\n    else {\n        return parseInt(String.fromCharCode(chr1) + String.fromCharCode(chr2));\n    }\n}\n\nfunction charCompatible(chr, codeset) {\n    var csa = codeSetAllowedFor(chr);\n    if (csa == CODESET.ANY) return true;\n    //if we need to change from current\n    if (csa == CODESET.AB) return true;\n    if (csa == CODESET.A && codeset == CODESET.A) return true;\n    if (csa == CODESET.B && codeset == CODESET.B) return true;\n    return false;\n}\n\nfunction codeSetAllowedFor(chr) {\n    if (chr >= 48 && chr <= 57) {\n        //0-9\n        return CODESET.ANY;\n    }\n    else if (chr >= 32 && chr <= 95) {\n        //0-9 A-Z\n        return CODESET.AB;\n    }\n    else {\n        //if non printable\n        return chr < 32 ? CODESET.A : CODESET.B;\n    }\n}\n\nvar Graphics = function(ctx, width, height) {\n\n    this.width = width;\n    this.height = height;\n    this.quiet = Math.round(this.width / 40);\n    \n    this.border_size   = 0;\n    this.padding_width = 0;\n\n    this.area = {\n        width : width - this.padding_width * 2 - this.quiet * 2,\n        height: height - this.border_size * 2,\n        top   : this.border_size - 4,\n        left  : this.padding_width + this.quiet\n    };\n\n    this.ctx = ctx;\n    this.fg = \"#000000\";\n    this.bg = \"#ffffff\";\n\n    // fill background\n    this.fillBgRect(0,0, width, height);\n\n    // fill center to create border\n    this.fillBgRect(0, this.border_size, width, height - this.border_size * 2);\n}\n\n//use native color\nGraphics.prototype._fillRect = function(x, y, width, height, color) {\n    this.ctx.setFillStyle(color)\n    this.ctx.fillRect(x, y, width, height)\n}\n\nGraphics.prototype.fillFgRect = function(x,y, width, height) {\n    this._fillRect(x, y, width, height, this.fg);\n}\n\nGraphics.prototype.fillBgRect = function(x,y, width, height) {\n    this._fillRect(x, y, width, height, this.bg);\n}\n\nvar PATTERNS = [\n    [2, 1, 2, 2, 2, 2, 0, 0],  // 0\n    [2, 2, 2, 1, 2, 2, 0, 0],  // 1\n    [2, 2, 2, 2, 2, 1, 0, 0],  // 2\n    [1, 2, 1, 2, 2, 3, 0, 0],  // 3\n    [1, 2, 1, 3, 2, 2, 0, 0],  // 4\n    [1, 3, 1, 2, 2, 2, 0, 0],  // 5\n    [1, 2, 2, 2, 1, 3, 0, 0],  // 6\n    [1, 2, 2, 3, 1, 2, 0, 0],  // 7\n    [1, 3, 2, 2, 1, 2, 0, 0],  // 8\n    [2, 2, 1, 2, 1, 3, 0, 0],  // 9\n    [2, 2, 1, 3, 1, 2, 0, 0],  // 10\n    [2, 3, 1, 2, 1, 2, 0, 0],  // 11\n    [1, 1, 2, 2, 3, 2, 0, 0],  // 12\n    [1, 2, 2, 1, 3, 2, 0, 0],  // 13\n    [1, 2, 2, 2, 3, 1, 0, 0],  // 14\n    [1, 1, 3, 2, 2, 2, 0, 0],  // 15\n    [1, 2, 3, 1, 2, 2, 0, 0],  // 16\n    [1, 2, 3, 2, 2, 1, 0, 0],  // 17\n    [2, 2, 3, 2, 1, 1, 0, 0],  // 18\n    [2, 2, 1, 1, 3, 2, 0, 0],  // 19\n    [2, 2, 1, 2, 3, 1, 0, 0],  // 20\n    [2, 1, 3, 2, 1, 2, 0, 0],  // 21\n    [2, 2, 3, 1, 1, 2, 0, 0],  // 22\n    [3, 1, 2, 1, 3, 1, 0, 0],  // 23\n    [3, 1, 1, 2, 2, 2, 0, 0],  // 24\n    [3, 2, 1, 1, 2, 2, 0, 0],  // 25\n    [3, 2, 1, 2, 2, 1, 0, 0],  // 26\n    [3, 1, 2, 2, 1, 2, 0, 0],  // 27\n    [3, 2, 2, 1, 1, 2, 0, 0],  // 28\n    [3, 2, 2, 2, 1, 1, 0, 0],  // 29\n    [2, 1, 2, 1, 2, 3, 0, 0],  // 30\n    [2, 1, 2, 3, 2, 1, 0, 0],  // 31\n    [2, 3, 2, 1, 2, 1, 0, 0],  // 32\n    [1, 1, 1, 3, 2, 3, 0, 0],  // 33\n    [1, 3, 1, 1, 2, 3, 0, 0],  // 34\n    [1, 3, 1, 3, 2, 1, 0, 0],  // 35\n    [1, 1, 2, 3, 1, 3, 0, 0],  // 36\n    [1, 3, 2, 1, 1, 3, 0, 0],  // 37\n    [1, 3, 2, 3, 1, 1, 0, 0],  // 38\n    [2, 1, 1, 3, 1, 3, 0, 0],  // 39\n    [2, 3, 1, 1, 1, 3, 0, 0],  // 40\n    [2, 3, 1, 3, 1, 1, 0, 0],  // 41\n    [1, 1, 2, 1, 3, 3, 0, 0],  // 42\n    [1, 1, 2, 3, 3, 1, 0, 0],  // 43\n    [1, 3, 2, 1, 3, 1, 0, 0],  // 44\n    [1, 1, 3, 1, 2, 3, 0, 0],  // 45\n    [1, 1, 3, 3, 2, 1, 0, 0],  // 46\n    [1, 3, 3, 1, 2, 1, 0, 0],  // 47\n    [3, 1, 3, 1, 2, 1, 0, 0],  // 48\n    [2, 1, 1, 3, 3, 1, 0, 0],  // 49\n    [2, 3, 1, 1, 3, 1, 0, 0],  // 50\n    [2, 1, 3, 1, 1, 3, 0, 0],  // 51\n    [2, 1, 3, 3, 1, 1, 0, 0],  // 52\n    [2, 1, 3, 1, 3, 1, 0, 0],  // 53\n    [3, 1, 1, 1, 2, 3, 0, 0],  // 54\n    [3, 1, 1, 3, 2, 1, 0, 0],  // 55\n    [3, 3, 1, 1, 2, 1, 0, 0],  // 56\n    [3, 1, 2, 1, 1, 3, 0, 0],  // 57\n    [3, 1, 2, 3, 1, 1, 0, 0],  // 58\n    [3, 3, 2, 1, 1, 1, 0, 0],  // 59\n    [3, 1, 4, 1, 1, 1, 0, 0],  // 60\n    [2, 2, 1, 4, 1, 1, 0, 0],  // 61\n    [4, 3, 1, 1, 1, 1, 0, 0],  // 62\n    [1, 1, 1, 2, 2, 4, 0, 0],  // 63\n    [1, 1, 1, 4, 2, 2, 0, 0],  // 64\n    [1, 2, 1, 1, 2, 4, 0, 0],  // 65\n    [1, 2, 1, 4, 2, 1, 0, 0],  // 66\n    [1, 4, 1, 1, 2, 2, 0, 0],  // 67\n    [1, 4, 1, 2, 2, 1, 0, 0],  // 68\n    [1, 1, 2, 2, 1, 4, 0, 0],  // 69\n    [1, 1, 2, 4, 1, 2, 0, 0],  // 70\n    [1, 2, 2, 1, 1, 4, 0, 0],  // 71\n    [1, 2, 2, 4, 1, 1, 0, 0],  // 72\n    [1, 4, 2, 1, 1, 2, 0, 0],  // 73\n    [1, 4, 2, 2, 1, 1, 0, 0],  // 74\n    [2, 4, 1, 2, 1, 1, 0, 0],  // 75\n    [2, 2, 1, 1, 1, 4, 0, 0],  // 76\n    [4, 1, 3, 1, 1, 1, 0, 0],  // 77\n    [2, 4, 1, 1, 1, 2, 0, 0],  // 78\n    [1, 3, 4, 1, 1, 1, 0, 0],  // 79\n    [1, 1, 1, 2, 4, 2, 0, 0],  // 80\n    [1, 2, 1, 1, 4, 2, 0, 0],  // 81\n    [1, 2, 1, 2, 4, 1, 0, 0],  // 82\n    [1, 1, 4, 2, 1, 2, 0, 0],  // 83\n    [1, 2, 4, 1, 1, 2, 0, 0],  // 84\n    [1, 2, 4, 2, 1, 1, 0, 0],  // 85\n    [4, 1, 1, 2, 1, 2, 0, 0],  // 86\n    [4, 2, 1, 1, 1, 2, 0, 0],  // 87\n    [4, 2, 1, 2, 1, 1, 0, 0],  // 88\n    [2, 1, 2, 1, 4, 1, 0, 0],  // 89\n    [2, 1, 4, 1, 2, 1, 0, 0],  // 90\n    [4, 1, 2, 1, 2, 1, 0, 0],  // 91\n    [1, 1, 1, 1, 4, 3, 0, 0],  // 92\n    [1, 1, 1, 3, 4, 1, 0, 0],  // 93\n    [1, 3, 1, 1, 4, 1, 0, 0],  // 94\n    [1, 1, 4, 1, 1, 3, 0, 0],  // 95\n    [1, 1, 4, 3, 1, 1, 0, 0],  // 96\n    [4, 1, 1, 1, 1, 3, 0, 0],  // 97\n    [4, 1, 1, 3, 1, 1, 0, 0],  // 98\n    [1, 1, 3, 1, 4, 1, 0, 0],  // 99\n    [1, 1, 4, 1, 3, 1, 0, 0],  // 100\n    [3, 1, 1, 1, 4, 1, 0, 0],  // 101\n    [4, 1, 1, 1, 3, 1, 0, 0],  // 102\n    [2, 1, 1, 4, 1, 2, 0, 0],  // 103\n    [2, 1, 1, 2, 1, 4, 0, 0],  // 104\n    [2, 1, 1, 2, 3, 2, 0, 0],  // 105\n    [2, 3, 3, 1, 1, 1, 2, 0]   // 106\n]\n\n", "var QR = (function () {\n\n    // alignment pattern\n    var adelta = [\n      0, 11, 15, 19, 23, 27, 31, // force 1 pat\n      16, 18, 20, 22, 24, 26, 28, 20, 22, 24, 24, 26, 28, 28, 22, 24, 24,\n      26, 26, 28, 28, 24, 24, 26, 26, 26, 28, 28, 24, 26, 26, 26, 28, 28\n      ];\n\n    // version block\n    var vpat = [\n        0xc94, 0x5bc, 0xa99, 0x4d3, 0xbf6, 0x762, 0x847, 0x60d,\n        0x928, 0xb78, 0x45d, 0xa17, 0x532, 0x9a6, 0x683, 0x8c9,\n        0x7ec, 0xec4, 0x1e1, 0xfab, 0x08e, 0xc1a, 0x33f, 0xd75,\n        0x250, 0x9d5, 0x6f0, 0x8ba, 0x79f, 0xb0b, 0x42e, 0xa64,\n        0x541, 0xc69\n    ];\n\n    // final format bits with mask: level << 3 | mask\n    var fmtword = [\n        0x77c4, 0x72f3, 0x7daa, 0x789d, 0x662f, 0x6318, 0x6c41, 0x6976,    //L\n        0x5412, 0x5125, 0x5e7c, 0x5b4b, 0x45f9, 0x40ce, 0x4f97, 0x4aa0,    //M\n        0x355f, 0x3068, 0x3f31, 0x3a06, 0x24b4, 0x2183, 0x2eda, 0x2bed,    //Q\n        0x1689, 0x13be, 0x1ce7, 0x19d0, 0x0762, 0x0255, 0x0d0c, 0x083b    //H\n    ];\n\n    // 4 per version: number of blocks 1,2; data width; ecc width\n    var eccblocks = [\n        1, 0, 19, 7, 1, 0, 16, 10, 1, 0, 13, 13, 1, 0, 9, 17,\n        1, 0, 34, 10, 1, 0, 28, 16, 1, 0, 22, 22, 1, 0, 16, 28,\n        1, 0, 55, 15, 1, 0, 44, 26, 2, 0, 17, 18, 2, 0, 13, 22,\n        1, 0, 80, 20, 2, 0, 32, 18, 2, 0, 24, 26, 4, 0, 9, 16,\n        1, 0, 108, 26, 2, 0, 43, 24, 2, 2, 15, 18, 2, 2, 11, 22,\n        2, 0, 68, 18, 4, 0, 27, 16, 4, 0, 19, 24, 4, 0, 15, 28,\n        2, 0, 78, 20, 4, 0, 31, 18, 2, 4, 14, 18, 4, 1, 13, 26,\n        2, 0, 97, 24, 2, 2, 38, 22, 4, 2, 18, 22, 4, 2, 14, 26,\n        2, 0, 116, 30, 3, 2, 36, 22, 4, 4, 16, 20, 4, 4, 12, 24,\n        2, 2, 68, 18, 4, 1, 43, 26, 6, 2, 19, 24, 6, 2, 15, 28,\n        4, 0, 81, 20, 1, 4, 50, 30, 4, 4, 22, 28, 3, 8, 12, 24,\n        2, 2, 92, 24, 6, 2, 36, 22, 4, 6, 20, 26, 7, 4, 14, 28,\n        4, 0, 107, 26, 8, 1, 37, 22, 8, 4, 20, 24, 12, 4, 11, 22,\n        3, 1, 115, 30, 4, 5, 40, 24, 11, 5, 16, 20, 11, 5, 12, 24,\n        5, 1, 87, 22, 5, 5, 41, 24, 5, 7, 24, 30, 11, 7, 12, 24,\n        5, 1, 98, 24, 7, 3, 45, 28, 15, 2, 19, 24, 3, 13, 15, 30,\n        1, 5, 107, 28, 10, 1, 46, 28, 1, 15, 22, 28, 2, 17, 14, 28,\n        5, 1, 120, 30, 9, 4, 43, 26, 17, 1, 22, 28, 2, 19, 14, 28,\n        3, 4, 113, 28, 3, 11, 44, 26, 17, 4, 21, 26, 9, 16, 13, 26,\n        3, 5, 107, 28, 3, 13, 41, 26, 15, 5, 24, 30, 15, 10, 15, 28,\n        4, 4, 116, 28, 17, 0, 42, 26, 17, 6, 22, 28, 19, 6, 16, 30,\n        2, 7, 111, 28, 17, 0, 46, 28, 7, 16, 24, 30, 34, 0, 13, 24,\n        4, 5, 121, 30, 4, 14, 47, 28, 11, 14, 24, 30, 16, 14, 15, 30,\n        6, 4, 117, 30, 6, 14, 45, 28, 11, 16, 24, 30, 30, 2, 16, 30,\n        8, 4, 106, 26, 8, 13, 47, 28, 7, 22, 24, 30, 22, 13, 15, 30,\n        10, 2, 114, 28, 19, 4, 46, 28, 28, 6, 22, 28, 33, 4, 16, 30,\n        8, 4, 122, 30, 22, 3, 45, 28, 8, 26, 23, 30, 12, 28, 15, 30,\n        3, 10, 117, 30, 3, 23, 45, 28, 4, 31, 24, 30, 11, 31, 15, 30,\n        7, 7, 116, 30, 21, 7, 45, 28, 1, 37, 23, 30, 19, 26, 15, 30,\n        5, 10, 115, 30, 19, 10, 47, 28, 15, 25, 24, 30, 23, 25, 15, 30,\n        13, 3, 115, 30, 2, 29, 46, 28, 42, 1, 24, 30, 23, 28, 15, 30,\n        17, 0, 115, 30, 10, 23, 46, 28, 10, 35, 24, 30, 19, 35, 15, 30,\n        17, 1, 115, 30, 14, 21, 46, 28, 29, 19, 24, 30, 11, 46, 15, 30,\n        13, 6, 115, 30, 14, 23, 46, 28, 44, 7, 24, 30, 59, 1, 16, 30,\n        12, 7, 121, 30, 12, 26, 47, 28, 39, 14, 24, 30, 22, 41, 15, 30,\n        6, 14, 121, 30, 6, 34, 47, 28, 46, 10, 24, 30, 2, 64, 15, 30,\n        17, 4, 122, 30, 29, 14, 46, 28, 49, 10, 24, 30, 24, 46, 15, 30,\n        4, 18, 122, 30, 13, 32, 46, 28, 48, 14, 24, 30, 42, 32, 15, 30,\n        20, 4, 117, 30, 40, 7, 47, 28, 43, 22, 24, 30, 10, 67, 15, 30,\n        19, 6, 118, 30, 18, 31, 47, 28, 34, 34, 24, 30, 20, 61, 15, 30\n    ];\n\n    // Galois field log table\n    var glog = [\n        0xff, 0x00, 0x01, 0x19, 0x02, 0x32, 0x1a, 0xc6, 0x03, 0xdf, 0x33, 0xee, 0x1b, 0x68, 0xc7, 0x4b,\n        0x04, 0x64, 0xe0, 0x0e, 0x34, 0x8d, 0xef, 0x81, 0x1c, 0xc1, 0x69, 0xf8, 0xc8, 0x08, 0x4c, 0x71,\n        0x05, 0x8a, 0x65, 0x2f, 0xe1, 0x24, 0x0f, 0x21, 0x35, 0x93, 0x8e, 0xda, 0xf0, 0x12, 0x82, 0x45,\n        0x1d, 0xb5, 0xc2, 0x7d, 0x6a, 0x27, 0xf9, 0xb9, 0xc9, 0x9a, 0x09, 0x78, 0x4d, 0xe4, 0x72, 0xa6,\n        0x06, 0xbf, 0x8b, 0x62, 0x66, 0xdd, 0x30, 0xfd, 0xe2, 0x98, 0x25, 0xb3, 0x10, 0x91, 0x22, 0x88,\n        0x36, 0xd0, 0x94, 0xce, 0x8f, 0x96, 0xdb, 0xbd, 0xf1, 0xd2, 0x13, 0x5c, 0x83, 0x38, 0x46, 0x40,\n        0x1e, 0x42, 0xb6, 0xa3, 0xc3, 0x48, 0x7e, 0x6e, 0x6b, 0x3a, 0x28, 0x54, 0xfa, 0x85, 0xba, 0x3d,\n        0xca, 0x5e, 0x9b, 0x9f, 0x0a, 0x15, 0x79, 0x2b, 0x4e, 0xd4, 0xe5, 0xac, 0x73, 0xf3, 0xa7, 0x57,\n        0x07, 0x70, 0xc0, 0xf7, 0x8c, 0x80, 0x63, 0x0d, 0x67, 0x4a, 0xde, 0xed, 0x31, 0xc5, 0xfe, 0x18,\n        0xe3, 0xa5, 0x99, 0x77, 0x26, 0xb8, 0xb4, 0x7c, 0x11, 0x44, 0x92, 0xd9, 0x23, 0x20, 0x89, 0x2e,\n        0x37, 0x3f, 0xd1, 0x5b, 0x95, 0xbc, 0xcf, 0xcd, 0x90, 0x87, 0x97, 0xb2, 0xdc, 0xfc, 0xbe, 0x61,\n        0xf2, 0x56, 0xd3, 0xab, 0x14, 0x2a, 0x5d, 0x9e, 0x84, 0x3c, 0x39, 0x53, 0x47, 0x6d, 0x41, 0xa2,\n        0x1f, 0x2d, 0x43, 0xd8, 0xb7, 0x7b, 0xa4, 0x76, 0xc4, 0x17, 0x49, 0xec, 0x7f, 0x0c, 0x6f, 0xf6,\n        0x6c, 0xa1, 0x3b, 0x52, 0x29, 0x9d, 0x55, 0xaa, 0xfb, 0x60, 0x86, 0xb1, 0xbb, 0xcc, 0x3e, 0x5a,\n        0xcb, 0x59, 0x5f, 0xb0, 0x9c, 0xa9, 0xa0, 0x51, 0x0b, 0xf5, 0x16, 0xeb, 0x7a, 0x75, 0x2c, 0xd7,\n        0x4f, 0xae, 0xd5, 0xe9, 0xe6, 0xe7, 0xad, 0xe8, 0x74, 0xd6, 0xf4, 0xea, 0xa8, 0x50, 0x58, 0xaf\n    ];\n\n    // Galios field exponent table\n    var gexp = [\n        0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1d, 0x3a, 0x74, 0xe8, 0xcd, 0x87, 0x13, 0x26,\n        0x4c, 0x98, 0x2d, 0x5a, 0xb4, 0x75, 0xea, 0xc9, 0x8f, 0x03, 0x06, 0x0c, 0x18, 0x30, 0x60, 0xc0,\n        0x9d, 0x27, 0x4e, 0x9c, 0x25, 0x4a, 0x94, 0x35, 0x6a, 0xd4, 0xb5, 0x77, 0xee, 0xc1, 0x9f, 0x23,\n        0x46, 0x8c, 0x05, 0x0a, 0x14, 0x28, 0x50, 0xa0, 0x5d, 0xba, 0x69, 0xd2, 0xb9, 0x6f, 0xde, 0xa1,\n        0x5f, 0xbe, 0x61, 0xc2, 0x99, 0x2f, 0x5e, 0xbc, 0x65, 0xca, 0x89, 0x0f, 0x1e, 0x3c, 0x78, 0xf0,\n        0xfd, 0xe7, 0xd3, 0xbb, 0x6b, 0xd6, 0xb1, 0x7f, 0xfe, 0xe1, 0xdf, 0xa3, 0x5b, 0xb6, 0x71, 0xe2,\n        0xd9, 0xaf, 0x43, 0x86, 0x11, 0x22, 0x44, 0x88, 0x0d, 0x1a, 0x34, 0x68, 0xd0, 0xbd, 0x67, 0xce,\n        0x81, 0x1f, 0x3e, 0x7c, 0xf8, 0xed, 0xc7, 0x93, 0x3b, 0x76, 0xec, 0xc5, 0x97, 0x33, 0x66, 0xcc,\n        0x85, 0x17, 0x2e, 0x5c, 0xb8, 0x6d, 0xda, 0xa9, 0x4f, 0x9e, 0x21, 0x42, 0x84, 0x15, 0x2a, 0x54,\n        0xa8, 0x4d, 0x9a, 0x29, 0x52, 0xa4, 0x55, 0xaa, 0x49, 0x92, 0x39, 0x72, 0xe4, 0xd5, 0xb7, 0x73,\n        0xe6, 0xd1, 0xbf, 0x63, 0xc6, 0x91, 0x3f, 0x7e, 0xfc, 0xe5, 0xd7, 0xb3, 0x7b, 0xf6, 0xf1, 0xff,\n        0xe3, 0xdb, 0xab, 0x4b, 0x96, 0x31, 0x62, 0xc4, 0x95, 0x37, 0x6e, 0xdc, 0xa5, 0x57, 0xae, 0x41,\n        0x82, 0x19, 0x32, 0x64, 0xc8, 0x8d, 0x07, 0x0e, 0x1c, 0x38, 0x70, 0xe0, 0xdd, 0xa7, 0x53, 0xa6,\n        0x51, 0xa2, 0x59, 0xb2, 0x79, 0xf2, 0xf9, 0xef, 0xc3, 0x9b, 0x2b, 0x56, 0xac, 0x45, 0x8a, 0x09,\n        0x12, 0x24, 0x48, 0x90, 0x3d, 0x7a, 0xf4, 0xf5, 0xf7, 0xf3, 0xfb, 0xeb, 0xcb, 0x8b, 0x0b, 0x16,\n        0x2c, 0x58, 0xb0, 0x7d, 0xfa, 0xe9, 0xcf, 0x83, 0x1b, 0x36, 0x6c, 0xd8, 0xad, 0x47, 0x8e, 0x00\n    ];\n\n    // Working buffers:\n    // data input and ecc append, image working buffer, fixed part of image, run lengths for badness\n    var strinbuf=[], eccbuf=[], qrframe=[], framask=[], rlens=[]; \n    // Control values - width is based on version, last 4 are from table.\n    var version, width, neccblk1, neccblk2, datablkw, eccblkwid;\n    var ecclevel = 2;\n    // set bit to indicate cell in qrframe is immutable.  symmetric around diagonal\n    function setmask(x, y)\n    {\n        var bt;\n        if (x > y) {\n            bt = x;\n            x = y;\n            y = bt;\n        }\n        // y*y = 1+3+5...\n        bt = y;\n        bt *= y;\n        bt += y;\n        bt >>= 1;\n        bt += x;\n        framask[bt] = 1;\n    }\n\n    // enter alignment pattern - black to qrframe, white to mask (later black frame merged to mask)\n    function putalign(x, y)\n    {\n        var j;\n\n        qrframe[x + width * y] = 1;\n        for (j = -2; j < 2; j++) {\n            qrframe[(x + j) + width * (y - 2)] = 1;\n            qrframe[(x - 2) + width * (y + j + 1)] = 1;\n            qrframe[(x + 2) + width * (y + j)] = 1;\n            qrframe[(x + j + 1) + width * (y + 2)] = 1;\n        }\n        for (j = 0; j < 2; j++) {\n            setmask(x - 1, y + j);\n            setmask(x + 1, y - j);\n            setmask(x - j, y - 1);\n            setmask(x + j, y + 1);\n        }\n    }\n\n    //========================================================================\n    // Reed Solomon error correction\n    // exponentiation mod N\n    function modnn(x)\n    {\n        while (x >= 255) {\n            x -= 255;\n            x = (x >> 8) + (x & 255);\n        }\n        return x;\n    }\n\n    var genpoly = [];\n\n    // Calculate and append ECC data to data block.  Block is in strinbuf, indexes to buffers given.\n    function appendrs(data, dlen, ecbuf, eclen)\n    {\n        var i, j, fb;\n\n        for (i = 0; i < eclen; i++)\n            strinbuf[ecbuf + i] = 0;\n        for (i = 0; i < dlen; i++) {\n            fb = glog[strinbuf[data + i] ^ strinbuf[ecbuf]];\n            if (fb != 255)     /* fb term is non-zero */\n                for (j = 1; j < eclen; j++)\n                    strinbuf[ecbuf + j - 1] = strinbuf[ecbuf + j] ^ gexp[modnn(fb + genpoly[eclen - j])];\n            else\n                for( j = ecbuf ; j < ecbuf + eclen; j++ )\n                    strinbuf[j] = strinbuf[j + 1];\n            strinbuf[ ecbuf + eclen - 1] = fb == 255 ? 0 : gexp[modnn(fb + genpoly[0])];\n        }\n    }\n\n    //========================================================================\n    // Frame data insert following the path rules\n\n    // check mask - since symmetrical use half.\n    function ismasked(x, y)\n    {\n        var bt;\n        if (x > y) {\n            bt = x;\n            x = y;\n            y = bt;\n        }\n        bt = y;\n        bt += y * y;\n        bt >>= 1;\n        bt += x;\n        return framask[bt];\n    }\n\n    //========================================================================\n    //  Apply the selected mask out of the 8.\n    function  applymask(m)\n    {\n        var x, y, r3x, r3y;\n\n        switch (m) {\n        case 0:\n            for (y = 0; y < width; y++)\n                for (x = 0; x < width; x++)\n                    if (!((x + y) & 1) && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n            break;\n        case 1:\n            for (y = 0; y < width; y++)\n                for (x = 0; x < width; x++)\n                    if (!(y & 1) && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n            break;\n        case 2:\n            for (y = 0; y < width; y++)\n                for (r3x = 0, x = 0; x < width; x++, r3x++) {\n                    if (r3x == 3)\n                        r3x = 0;\n                    if (!r3x && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n                }\n            break;\n        case 3:\n            for (r3y = 0, y = 0; y < width; y++, r3y++) {\n                if (r3y == 3)\n                    r3y = 0;\n                for (r3x = r3y, x = 0; x < width; x++, r3x++) {\n                    if (r3x == 3)\n                        r3x = 0;\n                    if (!r3x && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n                }\n            }\n            break;\n        case 4:\n            for (y = 0; y < width; y++)\n                for (r3x = 0, r3y = ((y >> 1) & 1), x = 0; x < width; x++, r3x++) {\n                    if (r3x == 3) {\n                        r3x = 0;\n                        r3y = !r3y;\n                    }\n                    if (!r3y && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n                }\n            break;\n        case 5:\n            for (r3y = 0, y = 0; y < width; y++, r3y++) {\n                if (r3y == 3)\n                    r3y = 0;\n                for (r3x = 0, x = 0; x < width; x++, r3x++) {\n                    if (r3x == 3)\n                        r3x = 0;\n                    if (!((x & y & 1) + !(!r3x | !r3y)) && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n                }\n            }\n            break;\n        case 6:\n            for (r3y = 0, y = 0; y < width; y++, r3y++) {\n                if (r3y == 3)\n                    r3y = 0;\n                for (r3x = 0, x = 0; x < width; x++, r3x++) {\n                    if (r3x == 3)\n                        r3x = 0;\n                    if (!(((x & y & 1) + (r3x && (r3x == r3y))) & 1) && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n                }\n            }\n            break;\n        case 7:\n            for (r3y = 0, y = 0; y < width; y++, r3y++) {\n                if (r3y == 3)\n                    r3y = 0;\n                for (r3x = 0, x = 0; x < width; x++, r3x++) {\n                    if (r3x == 3)\n                        r3x = 0;\n                    if (!(((r3x && (r3x == r3y)) + ((x + y) & 1)) & 1) && !ismasked(x, y))\n                        qrframe[x + y * width] ^= 1;\n                }\n            }\n            break;\n        }\n        return;\n    }\n\n    // Badness coefficients.\n    var N1 = 3, N2 = 3, N3 = 40, N4 = 10;\n\n    // Using the table of the length of each run, calculate the amount of bad image \n    // - long runs or those that look like finders; called twice, once each for X and Y\n    function badruns(length)\n    {\n        var i;\n        var runsbad = 0;\n        for (i = 0; i <= length; i++)\n            if (rlens[i] >= 5)\n                runsbad += N1 + rlens[i] - 5;\n        // BwBBBwB as in finder\n        for (i = 3; i < length - 1; i += 2)\n            if (rlens[i - 2] == rlens[i + 2]\n                && rlens[i + 2] == rlens[i - 1]\n                && rlens[i - 1] == rlens[i + 1]\n                && rlens[i - 1] * 3 == rlens[i]\n                // white around the black pattern? Not part of spec\n                && (rlens[i - 3] == 0 // beginning\n                    || i + 3 > length  // end\n                    || rlens[i - 3] * 3 >= rlens[i] * 4 || rlens[i + 3] * 3 >= rlens[i] * 4)\n               )\n                runsbad += N3;\n        return runsbad;\n    }\n\n    // Calculate how bad the masked image is - blocks, imbalance, runs, or finders.\n    function badcheck()\n    {\n        var x, y, h, b, b1;\n        var thisbad = 0;\n        var bw = 0;\n\n        // blocks of same color.\n        for (y = 0; y < width - 1; y++)\n            for (x = 0; x < width - 1; x++)\n                if ((qrframe[x + width * y] && qrframe[(x + 1) + width * y]\n                     && qrframe[x + width * (y + 1)] && qrframe[(x + 1) + width * (y + 1)]) // all black\n                    || !(qrframe[x + width * y] || qrframe[(x + 1) + width * y]\n                         || qrframe[x + width * (y + 1)] || qrframe[(x + 1) + width * (y + 1)])) // all white\n                    thisbad += N2;\n\n        // X runs\n        for (y = 0; y < width; y++) {\n            rlens[0] = 0;\n            for (h = b = x = 0; x < width; x++) {\n                if ((b1 = qrframe[x + width * y]) == b)\n                    rlens[h]++;\n                else\n                    rlens[++h] = 1;\n                b = b1;\n                bw += b ? 1 : -1;\n            }\n            thisbad += badruns(h);\n        }\n\n        // black/white imbalance\n        if (bw < 0)\n            bw = -bw;\n\n        var big = bw;\n        var count = 0;\n        big += big << 2;\n        big <<= 1;\n        while (big > width * width)\n            big -= width * width, count++;\n        thisbad += count * N4;\n\n        // Y runs\n        for (x = 0; x < width; x++) {\n            rlens[0] = 0;\n            for (h = b = y = 0; y < width; y++) {\n                if ((b1 = qrframe[x + width * y]) == b)\n                    rlens[h]++;\n                else\n                    rlens[++h] = 1;\n                b = b1;\n            }\n            thisbad += badruns(h);\n        }\n        return thisbad;\n    }\n\n    function genframe(instring)\n    {\n        var x, y, k, t, v, i, j, m;\n\n    // find the smallest version that fits the string\n        t = instring.length;\n        version = 0;\n        do {\n            version++;\n            k = (ecclevel - 1) * 4 + (version - 1) * 16;\n            neccblk1 = eccblocks[k++];\n            neccblk2 = eccblocks[k++];\n            datablkw = eccblocks[k++];\n            eccblkwid = eccblocks[k];\n            k = datablkw * (neccblk1 + neccblk2) + neccblk2 - 3 + (version <= 9);\n            if (t <= k)\n                break;\n        } while (version < 40);\n\n    // FIXME - insure that it fits insted of being truncated\n        width = 17 + 4 * version;\n\n    // allocate, clear and setup data structures\n        v = datablkw + (datablkw + eccblkwid) * (neccblk1 + neccblk2) + neccblk2;\n        for( t = 0; t < v; t++ )\n            eccbuf[t] = 0;\n        strinbuf = instring.slice(0);\n\n        for( t = 0; t < width * width; t++ )\n            qrframe[t] = 0;\n\n        for( t = 0 ; t < (width * (width + 1) + 1) / 2; t++)\n            framask[t] = 0;\n\n    // insert finders - black to frame, white to mask\n        for (t = 0; t < 3; t++) {\n            k = 0;\n            y = 0;\n            if (t == 1)\n                k = (width - 7);\n            if (t == 2)\n                y = (width - 7);\n            qrframe[(y + 3) + width * (k + 3)] = 1;\n            for (x = 0; x < 6; x++) {\n                qrframe[(y + x) + width * k] = 1;\n                qrframe[y + width * (k + x + 1)] = 1;\n                qrframe[(y + 6) + width * (k + x)] = 1;\n                qrframe[(y + x + 1) + width * (k + 6)] = 1;\n            }\n            for (x = 1; x < 5; x++) {\n                setmask(y + x, k + 1);\n                setmask(y + 1, k + x + 1);\n                setmask(y + 5, k + x);\n                setmask(y + x + 1, k + 5);\n            }\n            for (x = 2; x < 4; x++) {\n                qrframe[(y + x) + width * (k + 2)] = 1;\n                qrframe[(y + 2) + width * (k + x + 1)] = 1;\n                qrframe[(y + 4) + width * (k + x)] = 1;\n                qrframe[(y + x + 1) + width * (k + 4)] = 1;\n            }\n        }\n\n    // alignment blocks\n        if (version > 1) {\n            t = adelta[version];\n            y = width - 7;\n            for (;;) {\n                x = width - 7;\n                while (x > t - 3) {\n                    putalign(x, y);\n                    if (x < t)\n                        break;\n                    x -= t;\n                }\n                if (y <= t + 9)\n                    break;\n                y -= t;\n                putalign(6, y);\n                putalign(y, 6);\n            }\n        }\n\n    // single black\n        qrframe[8 + width * (width - 8)] = 1;\n\n    // timing gap - mask only\n        for (y = 0; y < 7; y++) {\n            setmask(7, y);\n            setmask(width - 8, y);\n            setmask(7, y + width - 7);\n        }\n        for (x = 0; x < 8; x++) {\n            setmask(x, 7);\n            setmask(x + width - 8, 7);\n            setmask(x, width - 8);\n        }\n\n    // reserve mask-format area\n        for (x = 0; x < 9; x++)\n            setmask(x, 8);\n        for (x = 0; x < 8; x++) {\n            setmask(x + width - 8, 8);\n            setmask(8, x);\n        }\n        for (y = 0; y < 7; y++)\n            setmask(8, y + width - 7);\n\n    // timing row/col\n        for (x = 0; x < width - 14; x++)\n            if (x & 1) {\n                setmask(8 + x, 6);\n                setmask(6, 8 + x);\n            }\n            else {\n                qrframe[(8 + x) + width * 6] = 1;\n                qrframe[6 + width * (8 + x)] = 1;\n            }\n\n    // version block\n        if (version > 6) {\n            t = vpat[version - 7];\n            k = 17;\n            for (x = 0; x < 6; x++)\n                for (y = 0; y < 3; y++, k--)\n                    if (1 & (k > 11 ? version >> (k - 12) : t >> k)) {\n                        qrframe[(5 - x) + width * (2 - y + width - 11)] = 1;\n                        qrframe[(2 - y + width - 11) + width * (5 - x)] = 1;\n                    }\n            else {\n                setmask(5 - x, 2 - y + width - 11);\n                setmask(2 - y + width - 11, 5 - x);\n            }\n        }\n\n    // sync mask bits - only set above for white spaces, so add in black bits\n        for (y = 0; y < width; y++)\n            for (x = 0; x <= y; x++)\n                if (qrframe[x + width * y])\n                    setmask(x, y);\n\n    // convert string to bitstream\n    // 8 bit data to QR-coded 8 bit data (numeric or alphanum, or kanji not supported)\n        v = strinbuf.length;\n\n    // string to array\n        for( i = 0 ; i < v; i++ )\n            eccbuf[i] = strinbuf.charCodeAt(i);\n        strinbuf = eccbuf.slice(0);\n\n    // calculate max string length\n        x = datablkw * (neccblk1 + neccblk2) + neccblk2;\n        if (v >= x - 2) {\n            v = x - 2;\n            if (version > 9)\n                v--;\n        }\n\n    // shift and repack to insert length prefix\n        i = v;\n        if (version > 9) {\n            strinbuf[i + 2] = 0;\n            strinbuf[i + 3] = 0;\n            while (i--) {\n                t = strinbuf[i];\n                strinbuf[i + 3] |= 255 & (t << 4);\n                strinbuf[i + 2] = t >> 4;\n            }\n            strinbuf[2] |= 255 & (v << 4);\n            strinbuf[1] = v >> 4;\n            strinbuf[0] = 0x40 | (v >> 12);\n        }\n        else {\n            strinbuf[i + 1] = 0;\n            strinbuf[i + 2] = 0;\n            while (i--) {\n                t = strinbuf[i];\n                strinbuf[i + 2] |= 255 & (t << 4);\n                strinbuf[i + 1] = t >> 4;\n            }\n            strinbuf[1] |= 255 & (v << 4);\n            strinbuf[0] = 0x40 | (v >> 4);\n        }\n    // fill to end with pad pattern\n        i = v + 3 - (version < 10);\n        while (i < x) {\n            strinbuf[i++] = 0xec;\n            // buffer has room    if (i == x)      break;\n            strinbuf[i++] = 0x11;\n        }\n\n    // calculate and append ECC\n\n    // calculate generator polynomial\n        genpoly[0] = 1;\n        for (i = 0; i < eccblkwid; i++) {\n            genpoly[i + 1] = 1;\n            for (j = i; j > 0; j--)\n                genpoly[j] = genpoly[j]\n                ? genpoly[j - 1] ^ gexp[modnn(glog[genpoly[j]] + i)] : genpoly[j - 1];\n            genpoly[0] = gexp[modnn(glog[genpoly[0]] + i)];\n        }\n        for (i = 0; i <= eccblkwid; i++)\n            genpoly[i] = glog[genpoly[i]]; // use logs for genpoly[] to save calc step\n\n    // append ecc to data buffer\n        k = x;\n        y = 0;\n        for (i = 0; i < neccblk1; i++) {\n            appendrs(y, datablkw, k, eccblkwid);\n            y += datablkw;\n            k += eccblkwid;\n        }\n        for (i = 0; i < neccblk2; i++) {\n            appendrs(y, datablkw + 1, k, eccblkwid);\n            y += datablkw + 1;\n            k += eccblkwid;\n        }\n    // interleave blocks\n        y = 0;\n        for (i = 0; i < datablkw; i++) {\n            for (j = 0; j < neccblk1; j++)\n                eccbuf[y++] = strinbuf[i + j * datablkw];\n            for (j = 0; j < neccblk2; j++)\n                eccbuf[y++] = strinbuf[(neccblk1 * datablkw) + i + (j * (datablkw + 1))];\n        }\n        for (j = 0; j < neccblk2; j++)\n            eccbuf[y++] = strinbuf[(neccblk1 * datablkw) + i + (j * (datablkw + 1))];\n        for (i = 0; i < eccblkwid; i++)\n            for (j = 0; j < neccblk1 + neccblk2; j++)\n                eccbuf[y++] = strinbuf[x + i + j * eccblkwid];\n        strinbuf = eccbuf;\n\n    // pack bits into frame avoiding masked area.\n        x = y = width - 1;\n        k = v = 1;         // up, minus\n        /* inteleaved data and ecc codes */\n        m = (datablkw + eccblkwid) * (neccblk1 + neccblk2) + neccblk2;\n        for (i = 0; i < m; i++) {\n            t = strinbuf[i];\n            for (j = 0; j < 8; j++, t <<= 1) {\n                if (0x80 & t)\n                    qrframe[x + width * y] = 1;\n                do {        // find next fill position\n                    if (v)\n                        x--;\n                    else {\n                        x++;\n                        if (k) {\n                            if (y != 0)\n                                y--;\n                            else {\n                                x -= 2;\n                                k = !k;\n                                if (x == 6) {\n                                    x--;\n                                    y = 9;\n                                }\n                            }\n                        }\n                        else {\n                            if (y != width - 1)\n                                y++;\n                            else {\n                                x -= 2;\n                                k = !k;\n                                if (x == 6) {\n                                    x--;\n                                    y -= 8;\n                                }\n                            }\n                        }\n                    }\n                    v = !v;\n                } while (ismasked(x, y));\n            }\n        }\n\n    // save pre-mask copy of frame\n        strinbuf = qrframe.slice(0);\n        t = 0;           // best\n        y = 30000;         // demerit\n    // for instead of while since in original arduino code\n    // if an early mask was \"good enough\" it wouldn't try for a better one\n    // since they get more complex and take longer.\n        for (k = 0; k < 8; k++) {\n            applymask(k);      // returns black-white imbalance\n            x = badcheck();\n            if (x < y) { // current mask better than previous best?\n                y = x;\n                t = k;\n            }\n            if (t == 7)\n                break;       // don't increment i to a void redoing mask\n            qrframe = strinbuf.slice(0); // reset for next pass\n        }\n        if (t != k)         // redo best mask - none good enough, last wasn't t\n            applymask(t);\n\n    // add in final mask/ecclevel bytes\n        y = fmtword[t + ((ecclevel - 1) << 3)];\n        // low byte\n        for (k = 0; k < 8; k++, y >>= 1)\n            if (y & 1) {\n                qrframe[(width - 1 - k) + width * 8] = 1;\n                if (k < 6)\n                    qrframe[8 + width * k] = 1;\n                else\n                    qrframe[8 + width * (k + 1)] = 1;\n            }\n        // high byte\n        for (k = 0; k < 7; k++, y >>= 1)\n            if (y & 1) {\n                qrframe[8 + width * (width - 7 + k)] = 1;\n                if (k)\n                    qrframe[(6 - k) + width * 8] = 1;\n                else\n                    qrframe[7 + width * 8] = 1;\n            }\n\n    // return image\n        return qrframe;\n    }\n\n    var _canvas = null,\n        _size = null;\n\n    var api = {\n\n        get ecclevel () {\n            return ecclevel;\n        },\n\n        set ecclevel (val) {\n            ecclevel = val;\n        },\n\n        get size () {\n            return _size;\n        },\n\n        set size (val) {\n            _size = val\n        },\n\n        get canvas () {\n            return _canvas;\n        },\n\n        set canvas (el) {\n            _canvas = el;\n        },\n\n        getFrame: function (string) {\n            return genframe(string);\n        },\n\n        draw: function (string, canvas, size, ecc) {\n            \n            ecclevel = ecc || ecclevel;\n            canvas = canvas || _canvas;\n\n            if (!canvas) {\n                console.warn('No canvas provided to draw QR code in!')\n                return;\n            }\n\n            size = size || _size || Math.min(canvas.width, canvas.height);\n\n            var frame = genframe(string),\n                ctx = canvas.ctx,\n                px = Math.round(size / (width + 8));\n\n            var roundedSize = px * (width + 8),\n                offset = Math.floor((size - roundedSize) / 2);\n\n            size = roundedSize;\n\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            ctx.setFillStyle('#000000');\n\n            for (var i = 0; i < width; i++) {\n                for (var j = 0; j < width; j++) {\n                    if (frame[j * width + i]) {\n                        ctx.fillRect(px * (4 + i) + offset, px * (4 + j) + offset, px, px);\n                    }\n                }\n            }\n            ctx.draw();\n        }\n    }\n\n    module.exports = {\n        api: api\n    }\n\n})()"]}