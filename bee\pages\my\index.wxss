.userinfo, .uploader, .tunnel {  
  /* height: 140rpx; */
  width: 100%;    
  border-left: none;
  border-right: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  transition: all 300ms ease;
}

.userinfo {
  padding-left: 120rpx;
}

.userinfo-avatar {
  width: 120rpx;
  height: 120rpx;
  margin: 20rpx;
  border-radius: 50%;
  background-size: cover;
  background-color: white;
  overflow: hidden;
}
.userinfo-avatar-img {
  width: 120rpx;
  height: 120rpx;
  margin-left: -28rpx;
}

.userinfo-nickname {
  width: 400rpx;
  height: 100rpx;
  font-size: 24rpx;
  color: #007aff;
  background-color: white;
  background-size: cover;
  text-align: left;  
  margin-left: 10px;
}

.userinfo-nickname::after {
  border: none;
}

.userinfo-nickname-wrapper {
  flex: 1;
}

/*  */
.my-bar{
  width: 100%;
  position: relative;
}
.my-bar .background{
  width: 100vw;
  height: 350rpx; 
}
.my-box{
  position: absolute;
  width: 100vw;
  top: 64rpx;
  left: 0;
}
.login-btn {
  width: 400rpx;
  margin: auto;
  margin-top: 32rpx;
}
.my-box .head-bar{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 32rpx;
}
.head-bar-left {
  display: flex;
  align-items: center;
}
.my-box .head-bar .head{
  height: 120rpx;
  width: 120rpx;
  border-radius: 50%;
}
.my-box .head-bar .name-box{
  width: 400rpx;
  height: 100rpx;
  margin: 20rpx;
  font-size: 30rpx;
}
.my-box .head-bar .name-box .state{
  height: 40rpx;
  width: 100rpx;
  background-color: #999;
  margin-top: 5rpx;
  font-size: 25rpx;
  color: #ffffff;
  text-align: center;
  line-height: 40rpx;
  border-radius: 20rpx;
}
.user-code {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.user-code .txt {
  font-size: 24rpx;
  color: #333;
}

.coupon-box{
  display: flex;
  justify-content: center;
  margin-top:32rpx ;
}
.coupon-box .coupon{
  display: flex;
  flex-direction: column;
  align-items: center;
}
.coupon-box .coupon .title1{
  font-size: 25rpx;
}
.coupon-box .coupon .num{
  font-size: 42rpx;
}
.coupon-box .balance-box{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 88rpx;
}
.coupon-box .balance-box .amount-box{
  display: flex;
  align-items: flex-end;
}
.coupon-box .balance-box .amount{
  font-size: 25rpx;
}
.coupon-box .balance-box .amount-box .amount1{
  font-size: 26rpx;
  margin-right: 5rpx;
}
.coupon-box .balance-box .amount-box .amount2{
  font-size: 42rpx;
}
.my-bar1{
  padding-top: 32rpx;
}
.my-bar1 .box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 50rpx ;
}
.my-bar1 .box .box-0{
  display: flex;
  align-items: center;
}
.my-bar1 .box .box-0 .icon{
  width: 60rpx;
  height: 60rpx;
}
.my-bar1 .box .box-0 .title{
  font-size: 28rpx;
  margin-left: 20rpx;
}

.my-bar1 .box .go{
  height: 45rpx;
  width: 45rpx;

}
.version {
  text-align: center;
  margin: 64rpx;
  color: #666;
  font-size: 26rpx;
}