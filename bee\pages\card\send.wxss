page {
  background: rgb(235, 234, 234);
}
.from {
  margin: 88rpx 32rpx;
  background: #fff;
  padding: 32rpx;
  padding-bottom: 88rpx;
  border-radius: 32rpx;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.pic {
  width: 100%;
  height: 0;
}
.title {
  text-align: center;
  font-weight: bold;
  padding: 48rpx;
}
.btn {
  margin-top: 64rpx;
}
.price {
  text-align: center;
  color: #e64340;
  font-size: 48rpx;
  font-weight: bold;
}
.price text {
  color: #333;
  font-size: 24rpx;
  font-weight: normal;
}
.remark {
  border: 1rpx dashed #999999 !important;
  padding: 32rpx !important;
  border-radius: 16rpx;
}