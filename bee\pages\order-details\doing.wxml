<view wx:if="{{apiOk && !orderList}}" class="no-order">
  <view class="content-1">{{ $t.order.empty.t1 }}</view>
  <view class="content-2">{{ $t.order.empty.t2 }}</view>
  <view class="to-index-btn" bindtap="toIndexPage">{{ $t.order.empty.btn }}</view>
</view>
<swiper class="swiper" indicator-dots circular bindchange="bindchange">
  <swiper-item wx:for="{{orderList}}" wx:for-item="order" wx:key="id">
    <scroll-view scroll-y class="scroll-view">
      <view class="container">      
        <view wx:if="{{!order.isNeedLogistics}}" class="status">
          <view class="txt">{{ $t.PickingUp.qudanhao }}</view>
          <view class="qucanghao">{{order.qudanhao}}</view>
          <view class="hexiaoma">
            <canvas class="hx-canvas" canvas-id="qrcode_{{index}}" />
          </view>
        </view>
        <view wx:if="{{order.isNeedLogistics}}" class="status">
          <van-icon name="logistics" size="88rpx" color="orange" />
          <view class="txt">{{ $t.PickingUp.Deliverying }}</view>
          <view class="hexiaoma">
            <canvas class="hx-canvas" canvas-id="qrcode_{{index}}" />
          </view>
        </view>
        <van-divider dashed />
        <van-cell wx:if="{{logisticsMap[order.id]}}" title="{{logisticsMap[order.id].linkMan}} / {{logisticsMap[order.id].mobile}}" label="{{logisticsMap[order.id].provinceStr}}{{logisticsMap[order.id].cityStr}}{{logisticsMap[order.id].areaStr}}{{logisticsMap[order.id].address}}" />
        <van-cell wx:for="{{goodsMap[order.id]}}" wx:for-item="goods" wx:key="id" title="{{goods.goodsName}} / x{{goods.number}}" label="{{goods.property}}" value="¥{{goods.amount}}" value-class="call-value" />
        <van-cell title="{{ $t.PickingUp.total }}" value="¥{{order.amountReal}}" value-class="call-value" />
        <view class="times">
          <view>{{ $t.order.dateAdd }}: {{order.dateAdd}}</view>
        </view>
      </view>
    </scroll-view>
  </swiper-item>
</swiper>