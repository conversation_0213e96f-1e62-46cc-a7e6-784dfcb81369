module.exports = {
  "code": 0,
  "data": [
      {
          "id": 536281,
          "isUse": true,
          "level": 1,
          "name": "季节限定",
          "paixu": 1,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 535929,
          "isUse": true,
          "level": 1,
          "name": "开胃前菜",
          "paixu": 2,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536433,
          "isUse": true,
          "level": 1,
          "name": "一网捞尽",
          "paixu": 3,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536639,
          "isUse": true,
          "level": 1,
          "name": "豆香四溢",
          "paixu": 4,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536776,
          "isUse": true,
          "level": 1,
          "name": "鸡鲜鸭香",
          "paixu": 5,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536244,
          "isUse": true,
          "level": 1,
          "name": "鱼米之乡",
          "paixu": 6,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536705,
          "isUse": true,
          "level": 1,
          "name": "大快朵颐",
          "paixu": 7,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536562,
          "isUse": true,
          "level": 1,
          "name": "慢火汤煲",
          "paixu": 8,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536169,
          "isUse": true,
          "level": 1,
          "name": "四季时蔬",
          "paixu": 9,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536750,
          "isUse": true,
          "level": 1,
          "name": "点心主食",
          "paixu": 10,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536589,
          "isUse": true,
          "level": 1,
          "name": "待客糖水",
          "paixu": 11,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 535925,
          "isUse": true,
          "level": 1,
          "name": "饮料",
          "paixu": 12,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536213,
          "isUse": true,
          "level": 1,
          "name": "超值套餐",
          "paixu": 13,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536076,
          "isUse": true,
          "level": 1,
          "name": "活动套餐",
          "paixu": 14,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536472,
          "isUse": true,
          "level": 1,
          "name": "酒水",
          "paixu": 15,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      },
      {
          "id": 536683,
          "isUse": true,
          "level": 1,
          "name": "其它",
          "paixu": 16,
          "pid": 0,
          "shopId": 0,
          "userId": 73714
      }
  ],
  "msg": "success"
}