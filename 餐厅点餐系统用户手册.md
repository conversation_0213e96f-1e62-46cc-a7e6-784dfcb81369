# 餐厅点餐系统用户手册

## 目录
1. [系统概述](#1-系统概述)
2. [管理员操作手册](#2-管理员操作手册)
3. [顾客操作手册](#3-顾客操作手册)
4. [常见问题解答](#4-常见问题解答)
5. [技术支持](#5-技术支持)

## 1. 系统概述

### 1.1 系统介绍
张氏餐饮点餐系统是一套完整的餐厅数字化解决方案，包含：
- **后台管理系统**: 供餐厅管理员使用，管理餐品、订单、支付等
- **微信小程序**: 供顾客使用，实现在线点餐、支付、取餐等功能

### 1.2 系统特色
- 🍽️ 完整的餐厅业务流程覆盖
- 📱 微信小程序便捷点餐
- 💰 多种支付方式支持
- 📊 实时数据统计分析
- 🔒 完善的权限管理体系

### 1.3 使用角色
- **系统管理员**: 负责系统配置、用户管理、权限分配
- **餐厅管理员**: 负责餐品管理、订单处理、营销活动
- **收银员**: 负责订单确认、支付处理、退款操作
- **顾客**: 通过小程序进行点餐、支付、取餐

## 2. 管理员操作手册

### 2.1 系统登录

#### 2.1.1 登录步骤
1. 打开浏览器，访问管理后台地址
2. 输入用户名和密码
3. 输入验证码
4. 点击"登录"按钮

【登录页面截图】

#### 2.1.2 首次登录
- 默认管理员账号：admin
- 默认密码：admin123
- 首次登录后请及时修改密码

### 2.2 餐品管理

#### 2.2.1 餐品分类管理

**功能位置**: 餐厅管理 → 餐品分类

**主要功能**:
- 新增分类
- 编辑分类
- 删除分类
- 分类排序
- 启用/禁用分类

**操作步骤**:
1. 点击"新增"按钮
2. 填写分类信息：
   - 分类名称（必填）
   - 父级分类（可选，支持多级分类）
   - 分类描述
   - 分类图片
   - 排序序号
   - 分类状态
3. 点击"确定"保存

【餐品分类管理截图】

#### 2.2.2 餐品信息管理

**功能位置**: 餐厅管理 → 餐品管理

**主要功能**:
- 新增餐品
- 编辑餐品信息
- 删除餐品
- 上架/下架餐品
- 库存管理
- 推荐设置

**操作步骤**:
1. 点击"新增"按钮
2. 填写餐品基本信息：
   - 餐品名称（必填）
   - 所属分类（必填）
   - 餐品描述
   - 餐品图片
   - 基础价格（必填）
   - 当前库存
   - 库存预警阈值
3. 设置餐品属性：
   - 是否推荐
   - 是否热销
   - 上架状态
   - 定时上下架时间
4. 点击"确定"保存

【餐品管理截图】

#### 2.2.3 餐品规格管理

**功能位置**: 餐厅管理 → 餐品规格

**主要功能**:
- 为餐品添加规格选项
- 设置规格价格调整
- 管理规格库存

**规格类型**:
- 大小规格：大份、中份、小份
- 辣度等级：不辣、微辣、中辣、重辣
- 温度选择：热饮、温饮、冰饮
- 甜度选择：无糖、少糖、正常、多糖
- 加料选择：额外配菜、调料等

**操作步骤**:
1. 选择要添加规格的餐品
2. 点击"新增规格"
3. 填写规格信息：
   - 规格名称
   - 规格类型
   - 规格值
   - 价格调整（可为正负数）
   - 库存调整
4. 设置排序和状态
5. 点击"确定"保存

【餐品规格管理截图】

### 2.3 订单管理

#### 2.3.1 订单列表查看

**功能位置**: 餐厅管理 → 订单管理

**主要功能**:
- 查看所有订单
- 按状态筛选订单
- 按时间范围查询
- 订单详情查看
- 订单状态更新

**订单状态说明**:
- 🟡 待支付：订单已创建，等待顾客支付
- 🔵 已支付：订单已支付，等待制作
- 🟠 制作中：订单正在制作中
- 🟢 已完成：订单制作完成，可以取餐
- 🔴 已取消：订单已取消
- ⚫ 已退款：订单已退款

【订单管理截图】

#### 2.3.2 订单处理流程

**标准流程**:
1. 顾客下单 → 待支付
2. 顾客支付 → 已支付
3. 开始制作 → 制作中
4. 制作完成 → 已完成
5. 顾客取餐 → 订单结束

**异常处理**:
- 顾客取消订单 → 已取消
- 申请退款 → 已退款

#### 2.3.3 订单详情管理

**功能位置**: 点击订单列表中的"详情"按钮

**可查看信息**:
- 订单基本信息（订单号、桌号、取餐号）
- 顾客信息（姓名、电话）
- 订单商品详情
- 支付信息
- 时间信息（下单、支付、完成时间）

**可执行操作**:
- 更新订单状态
- 修改预计制作时间
- 添加备注信息
- 打印订单

【订单详情截图】

### 2.4 支付管理

#### 2.4.1 支付记录查询

**功能位置**: 餐厅管理 → 支付管理

**主要功能**:
- 查看所有支付记录
- 按支付方式筛选
- 按支付状态筛选
- 支付流水导出

**支付方式**:
- 微信支付
- 支付宝
- 现金
- 银行卡
- 会员卡

【支付管理截图】

#### 2.4.2 退款管理

**功能位置**: 餐厅管理 → 退款管理

**退款流程**:
1. 顾客申请退款或管理员发起退款
2. 填写退款原因
3. 确认退款金额
4. 处理退款（自动或手动）
5. 退款完成

**操作步骤**:
1. 在订单详情页点击"申请退款"
2. 填写退款信息：
   - 退款金额
   - 退款原因
3. 点击"确认退款"
4. 系统自动处理或手动确认

【退款管理截图】

### 2.5 营销管理

#### 2.5.1 广告轮播管理

**功能位置**: 餐厅管理 → 广告管理

**主要功能**:
- 添加轮播图
- 设置广告位置
- 定时上下线
- 点击统计

**广告位置**:
- 首页轮播：小程序首页轮播图
- 分类页横幅：分类页面横幅广告
- 餐品详情页：餐品详情页广告

**操作步骤**:
1. 点击"新增广告"
2. 填写广告信息：
   - 广告标题
   - 广告描述
   - 广告图片（建议尺寸：750x300px）
   - 跳转链接（可选）
   - 广告位置
   - 开始时间
   - 结束时间
3. 设置排序和状态
4. 点击"确定"保存

【广告管理截图】

### 2.6 数据统计

#### 2.6.1 销售统计

**功能位置**: 首页仪表盘

**统计指标**:
- 今日销售额
- 今日订单数
- 热销餐品排行
- 支付方式分布
- 订单状态分布

#### 2.6.2 报表导出

**支持导出的报表**:
- 订单明细报表
- 销售汇总报表
- 餐品销量报表
- 支付流水报表

**导出步骤**:
1. 选择报表类型
2. 设置时间范围
3. 选择导出格式（Excel/PDF）
4. 点击"导出"按钮

【数据统计截图】

### 2.7 系统管理

#### 2.7.1 用户管理

**功能位置**: 系统管理 → 用户管理

**主要功能**:
- 新增用户
- 编辑用户信息
- 重置密码
- 分配角色
- 启用/禁用用户

#### 2.7.2 角色权限管理

**功能位置**: 系统管理 → 角色管理

**预设角色**:
- 超级管理员：拥有所有权限
- 餐厅管理员：餐厅业务管理权限
- 收银员：订单和支付处理权限

#### 2.7.3 系统配置

**功能位置**: 系统管理 → 参数设置

**可配置参数**:
- 系统名称
- 系统Logo
- 联系方式
- 营业时间
- 配送范围
- 起送金额

【系统管理截图】

## 3. 顾客操作手册

### 3.1 小程序使用

#### 3.1.1 进入小程序

**方式一：微信搜索**
1. 打开微信
2. 点击顶部搜索框
3. 搜索"张氏餐饮"
4. 点击小程序进入

**方式二：扫码进入**
1. 使用微信扫一扫
2. 扫描餐厅提供的小程序码
3. 点击进入小程序

【小程序入口截图】

#### 3.1.2 首页功能

**主要功能**:
- 轮播图展示最新活动
- 推荐餐品快速选择
- 快捷功能入口
- 公告通知查看

**快捷入口**:
- 🍽️ 立即点餐
- 🎫 取号排队
- 📋 我的订单
- 👤 个人中心

【小程序首页截图】

### 3.2 点餐流程

#### 3.2.1 浏览餐品

**操作步骤**:
1. 点击底部"点餐"标签
2. 选择餐品分类
3. 浏览分类下的餐品
4. 点击餐品查看详情

**餐品信息**:
- 餐品图片
- 餐品名称
- 餐品描述
- 价格信息
- 销量和评分
- 规格选择

【餐品浏览截图】

#### 3.2.2 选择规格

**规格类型**:
- **大小规格**: 大份(+5元)、中份、小份(-3元)
- **辣度选择**: 不辣、微辣、中辣、重辣
- **温度选择**: 热饮、温饮、冰饮
- **甜度选择**: 无糖、少糖、正常、多糖
- **加料选择**: 额外配菜、调料等

**操作步骤**:
1. 在餐品详情页选择规格
2. 调整数量
3. 点击"加入购物车"

【规格选择截图】

#### 3.2.3 购物车管理

**功能位置**: 点餐页面底部购物车图标

**主要功能**:
- 查看已选餐品
- 修改数量
- 删除商品
- 查看总价
- 结算下单

**操作步骤**:
1. 点击购物车图标
2. 确认商品信息
3. 修改数量或删除不需要的商品
4. 点击"去结算"

【购物车截图】

### 3.3 下单支付

#### 3.3.1 确认订单

**需要填写的信息**:
- 用餐方式（堂食/外带）
- 桌号（堂食时必填）
- 联系人姓名
- 联系电话
- 备注信息（可选）

**操作步骤**:
1. 选择用餐方式
2. 填写必要信息
3. 确认订单金额
4. 点击"提交订单"

【订单确认截图】

#### 3.3.2 支付订单

**支付方式**:
- 微信支付（推荐）
- 支付宝
- 到店现金支付

**操作步骤**:
1. 选择支付方式
2. 点击"立即支付"
3. 完成支付验证
4. 支付成功后获得取餐号

【支付页面截图】

### 3.4 取餐流程

#### 3.4.1 查看订单状态

**功能位置**: 底部"取餐"标签

**订单状态**:
- ⏳ 制作中：订单正在制作，请耐心等待
- ✅ 制作完成：订单已完成，请及时取餐
- 📢 叫号中：正在叫号，请注意听取

**操作步骤**:
1. 点击"取餐"标签
2. 查看当前订单状态
3. 等待制作完成通知
4. 凭取餐号到前台取餐

【取餐页面截图】

#### 3.4.2 取餐号说明

**取餐号格式**: A001、B002等
- 字母代表餐品类型或制作区域
- 数字代表当日流水号

**注意事项**:
- 请记住您的取餐号
- 制作完成后会有语音叫号
- 如超过10分钟未取餐，请主动询问

### 3.5 订单管理

#### 3.5.1 查看订单历史

**功能位置**: 个人中心 → 我的订单

**可查看信息**:
- 订单号和下单时间
- 订单状态
- 餐品详情
- 支付金额
- 取餐号

**操作功能**:
- 再次购买
- 申请退款（限时）
- 订单评价

【订单历史截图】

#### 3.5.2 申请退款

**退款条件**:
- 订单状态为"已支付"或"制作中"
- 距离下单时间不超过30分钟
- 餐品尚未开始制作

**操作步骤**:
1. 在订单详情页点击"申请退款"
2. 选择退款原因
3. 填写退款说明
4. 提交退款申请
5. 等待商家处理

【退款申请截图】

### 3.6 会员功能

#### 3.6.1 会员注册

**注册方式**:
- 首次使用小程序自动注册
- 完善个人信息获得会员权益

**会员权益**:
- 积分累积
- 生日优惠
- 会员专享价格
- 优先制作

#### 3.6.2 积分系统

**积分获得**:
- 每消费1元获得1积分
- 完成订单评价获得额外积分
- 参与活动获得奖励积分

**积分使用**:
- 100积分 = 1元现金
- 兑换优惠券
- 兑换小礼品

【会员中心截图】

### 3.7 其他功能

#### 3.7.1 排队取号

**功能位置**: 底部"取号"标签

**使用场景**:
- 用餐高峰期需要排队
- 提前取号减少等待时间

**操作步骤**:
1. 点击"取号"标签
2. 选择用餐人数
3. 点击"立即取号"
4. 获得排队号码
5. 等待叫号

【排队取号截图】

#### 3.7.2 意见反馈

**功能位置**: 个人中心 → 意见反馈

**反馈类型**:
- 餐品质量问题
- 服务态度问题
- 系统使用问题
- 建议和意见

**操作步骤**:
1. 选择反馈类型
2. 详细描述问题
3. 上传相关图片（可选）
4. 提交反馈
5. 等待客服回复

【意见反馈截图】

## 4. 常见问题解答

### 4.1 管理员常见问题

**Q1: 忘记管理员密码怎么办？**
A1: 请联系系统管理员重置密码，或通过数据库直接修改密码字段。

**Q2: 如何批量导入餐品信息？**
A2: 可以使用Excel模板批量导入，在餐品管理页面点击"导入"按钮下载模板。

**Q3: 订单状态更新不及时怎么处理？**
A3: 检查网络连接，刷新页面，或联系技术支持检查系统状态。

**Q4: 如何设置餐品的定时上下架？**
A4: 在餐品编辑页面设置"定时上架时间"和"定时下架时间"，系统会自动执行。

### 4.2 顾客常见问题

**Q1: 小程序打不开怎么办？**
A1: 检查微信版本是否最新，或尝试删除小程序重新搜索进入。

**Q2: 支付失败怎么处理？**
A2: 检查网络连接和支付账户余额，或选择其他支付方式。

**Q3: 取餐号忘记了怎么办？**
A3: 在小程序"取餐"页面可以查看当前订单的取餐号。

**Q4: 可以修改已提交的订单吗？**
A4: 已支付的订单无法修改，如需变更请申请退款后重新下单。

**Q5: 积分什么时候到账？**
A5: 订单完成后积分会自动到账，如有延迟请联系客服。

### 4.3 技术问题

**Q1: 系统运行缓慢怎么办？**
A1: 检查服务器资源使用情况，清理数据库日志，优化查询语句。

**Q2: 数据库连接失败？**
A2: 检查数据库服务状态，确认连接配置正确，检查网络连通性。

**Q3: 小程序无法获取用户信息？**
A3: 检查小程序权限设置，确认用户已授权，检查API接口状态。

## 5. 技术支持

### 5.1 联系方式

**技术支持热线**: 400-xxx-xxxx  
**工作时间**: 周一至周日 9:00-22:00  
**邮箱支持**: <EMAIL>  
**在线客服**: 小程序内"联系客服"功能  

### 5.2 服务内容

- 系统安装部署
- 功能使用培训
- 故障排除支持
- 系统升级维护
- 数据备份恢复

### 5.3 培训服务

**管理员培训**:
- 系统基础操作培训（2小时）
- 高级功能使用培训（4小时）
- 数据分析和报表培训（2小时）

**员工培训**:
- 订单处理流程培训（1小时）
- 支付和退款操作培训（1小时）
- 常见问题处理培训（1小时）

### 5.4 系统维护

**定期维护**:
- 每周数据备份
- 每月系统性能检查
- 每季度安全更新
- 每年系统升级

**紧急支持**:
- 7×24小时故障响应
- 1小时内响应重大故障
- 4小时内解决一般问题
- 24小时内解决复杂问题

---

*本手册版本: v1.0*  
*最后更新时间: 2025-07-03*  
*文档维护: 技术支持团队*

**使用建议**: 
- 建议管理员定期查看本手册了解新功能
- 建议为员工提供操作培训
- 如有疑问请及时联系技术支持
