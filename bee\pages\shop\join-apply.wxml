<van-field
  label="{{ $t.shop.name }}"
  model:value="{{ name }}"
  placeholder="{{ $t.shop.namePlaceholder }}"
  clearable
/>
<van-field
  label="{{ $t.shop.mobile }}"
  type="number"
  model:value="{{ mobile }}"
  placeholder="{{ $t.shop.mobilePlaceholder }}"
  clearable
/>
<view style="margin-top:16rpx;padding-left:32rpx;">
  <van-uploader
    accept="media"
    multiple
    upload-text="{{ $t.feedback.imageOrVideo }}"
    image-fit="aspectFill"
    file-list="{{ picsList }}"
    bind:after-read="afterPicRead"
    bind:delete="afterPicDel"
  />
</view>
<van-field
  model:value="{{ content }}"
  placeholder="{{ $t.shop.contentPlaceholder }}"
  type="textarea"
  autosize="{{ autosize }}"
/>
<view class="block-btn btn">
  <van-button type="primary" block bind:click="bindSave">{{ $t.shop.joinBtn }}</van-button>
</view>